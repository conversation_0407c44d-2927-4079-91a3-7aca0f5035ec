#!/usr/bin/env node

// Test script to verify the updated vote functionality
const BASE_URL = 'http://localhost:3001';

// Test message ID (you can replace this with an actual message ID from your database)
const testMessageId = '9d1afd70-ebad-4678-af71-ebaaa41e3f04';

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    console.error('Request failed:', error);
    return { status: 500, error: error.message };
  }
}

async function testVoteByMessageIds() {
  console.log('\n🧪 Testing vote retrieval by messageIds...');
  
  const result = await makeRequest(`${BASE_URL}/api/vote?messageIds=${testMessageId}`);
  console.log('  Status:', result.status);
  console.log('  Data:', result.data);
  
  if (result.status === 200) {
    console.log('  ✅ Successfully retrieved votes by messageIds');
    return true;
  } else {
    console.log('  ❌ Failed to retrieve votes by messageIds');
    return false;
  }
}

async function testVoteMessage() {
  console.log('\n🧪 Testing vote message (upvote)...');
  
  const result = await makeRequest(`${BASE_URL}/api/vote`, {
    method: 'PATCH',
    body: JSON.stringify({
      messageId: testMessageId,
      type: 'up',
    }),
  });
  
  console.log('  Status:', result.status);
  console.log('  Response:', result.data || result.error);
  
  if (result.status === 200) {
    console.log('  ✅ Successfully voted on message');
    return true;
  } else {
    console.log('  ❌ Failed to vote on message');
    return false;
  }
}

async function testVotePersistence() {
  console.log('\n🧪 Testing vote persistence...');
  
  // First vote on the message
  await testVoteMessage();
  
  // Then retrieve votes to see if it persisted
  const result = await makeRequest(`${BASE_URL}/api/vote?messageIds=${testMessageId}`);
  console.log('  Retrieved votes:', result.data);
  
  if (Array.isArray(result.data)) {
    const ourVote = result.data.find(vote => vote.messageId === testMessageId);
    if (ourVote) {
      console.log('  ✅ Vote persisted successfully:', ourVote);
      return true;
    }
  }
  
  console.log('  ❌ Vote did not persist');
  return false;
}

async function runTests() {
  console.log('🚀 Starting vote functionality tests...');
  console.log('📝 Using test message ID:', testMessageId);
  
  const tests = [
    testVoteByMessageIds,
    testVoteMessage,
    testVotePersistence,
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    try {
      const result = await test();
      if (result) passed++;
    } catch (error) {
      console.error('Test failed with error:', error);
    }
  }
  
  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Vote functionality is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

// Run the tests
runTests().catch(console.error);
