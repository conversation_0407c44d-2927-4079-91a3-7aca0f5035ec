import { RateLimiter } from 'limiter';

// Create a Map to store rate limiters for each IP
const ipRateLimiters = new Map<string, RateLimiter>();

export class RateLimiterService {
  private static readonly REQUESTS_PER_MINUTE = 100;
  private static readonly INTERVAL_MS = 60000; // 1 minute in milliseconds

  static getRateLimiter(ip: string): RateLimiter {
    let limiter = ipRateLimiters.get(ip);

    if (!limiter) {
      limiter = new RateLimiter({
        tokensPerInterval: RateLimiterService.REQUESTS_PER_MINUTE,
        interval: 'minute',
      });
      ipRateLimiters.set(ip, limiter);
    }

    return limiter;
  }

  static async checkRateLimit(ip: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const limiter = this.getRateLimiter(ip);
    const hasToken = await limiter.tryRemoveTokens(1);

    // Calculate the next reset time based on the current time and interval
    const now = Date.now();
    const nextReset = Math.ceil(now / this.INTERVAL_MS) * this.INTERVAL_MS;

    return {
      allowed: hasToken,
      remaining: limiter.getTokensRemaining(),
      resetTime: nextReset,
    };
  }
}
