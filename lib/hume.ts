import { fetchAccessToken } from 'hume';
export const getHumeAccessToken = async () => {
  // Reads `HUME_API_KEY` and `HUME_SECRET_KEY` from environment variables
  const HUME_API_KEY = process.env.HUME_API_KEY;
  const HUME_SECRET_KEY = process.env.HUME_CLIENT_SECRET;

  const accessToken = await fetchAccessToken({
    apiKey: HUME_API_KEY ?? '',
    secretKey: HUME_SECRET_KEY ?? '',
  });

  if (accessToken === 'undefined') {
    console.log('access token null');
    return null;
  }

  return accessToken ?? null;
};
