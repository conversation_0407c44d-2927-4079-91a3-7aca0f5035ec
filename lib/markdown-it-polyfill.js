// Polyfill for markdown-it isSpace function that's missing in v14.x ESM builds
// This function checks if a character is a Unicode whitespace character

// Unicode whitespace characters based on markdown-it's implementation
const WHITESPACE_CHARS = [
  0x09, // \t
  0x0A, // \n
  0x0B, // \v
  0x0C, // \f
  0x0D, // \r
  0x20, // space
  0xA0, // non-breaking space
  0x1680, // ogham space mark
  0x180E, // mongolian vowel separator
  0x2000, // en quad
  0x2001, // em quad
  0x2002, // en space
  0x2003, // em space
  0x2004, // three-per-em space
  0x2005, // four-per-em space
  0x2006, // six-per-em space
  0x2007, // figure space
  0x2008, // punctuation space
  0x2009, // thin space
  0x200A, // hair space
  0x200B, // zero width space
  0x200C, // zero width non-joiner
  0x200D, // zero width joiner
  0x2028, // line separator
  0x2029, // paragraph separator
  0x202F, // narrow no-break space
  0x205F, // medium mathematical space
  0x3000, // ideographic space
  0xFEFF  // zero width no-break space
];

// Create a Set for faster lookup
const WHITESPACE_SET = new Set(WHITESPACE_CHARS);

// isSpace function implementation
function isSpace(code) {
  return WHITESPACE_SET.has(code);
}

// More aggressive polyfill approach - patch the global scope immediately
(function() {
  // Polyfill for global access
  if (typeof globalThis !== 'undefined') {
    globalThis.isSpace = isSpace;
  }
  if (typeof window !== 'undefined') {
    window.isSpace = isSpace;
  }
  if (typeof global !== 'undefined') {
    global.isSpace = isSpace;
  }

  // For browser environments, also patch the window object
  if (typeof document !== 'undefined') {
    // Create a script tag to inject the function into the global scope
    const script = document.createElement('script');
    script.textContent = `
      window.isSpace = function(code) {
        const WHITESPACE_SET = new Set([
          0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x20, 0xA0, 0x1680, 0x180E,
          0x2000, 0x2001, 0x2002, 0x2003, 0x2004, 0x2005, 0x2006, 0x2007,
          0x2008, 0x2009, 0x200A, 0x200B, 0x200C, 0x200D, 0x2028, 0x2029,
          0x202F, 0x205F, 0x3000, 0xFEFF
        ]);
        return WHITESPACE_SET.has(code);
      };
      globalThis.isSpace = window.isSpace;
    `;
    document.head.appendChild(script);
  }
})();

export { isSpace };
