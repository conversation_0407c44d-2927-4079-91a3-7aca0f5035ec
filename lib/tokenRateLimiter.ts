// Create a map to store rate limiters and token counts for each user
const userRateLimiters = new Map<
  string,
  {
    tokensUsed: number;
    lastReset: number;
  }
>();

const TOKEN_LIMIT = 200000; // Maximum tokens allowed per interval
const INTERVAL_MS = 60 * 1000; // 1 minute in milliseconds

// Function to get or create a rate limiter for a user
export function getUserRateLimiter(userId: string): { tokensUsed: number } {
  const now = Date.now();

  if (!userRateLimiters.has(userId)) {
    userRateLimiters.set(userId, {
      tokensUsed: 0,
      lastReset: now,
    });
  }

  const userData = userRateLimiters.get(userId)!;
  console.log(`[getUserRateLimiter]🔍  user ${userId}:`, {
    userData: userData,
  });
  // Reset token count if interval has passed
  if (now - userData.lastReset >= INTERVAL_MS) {
    console.log(`🔄 Resetting token count for user ${userId}`, {
      previousTokensUsed: userData.tokensUsed,
      timestamp: new Date(now).toISOString(),
    });
    userData.tokensUsed = 0;
    userData.lastReset = now;
  }

  return {
    tokensUsed: userData.tokensUsed,
  };
}

// Function to check if a user has enough tokens remaining for an estimated cost
export function canProceedWithTokens(
  userId: string,
  estimatedTokens: number,
): boolean {
  const { tokensUsed } = getUserRateLimiter(userId);

  if (tokensUsed + estimatedTokens > TOKEN_LIMIT) {
    console.warn(`🚦 Rate limit check failed for user ${userId}:`, {
      currentTokensUsed: tokensUsed,
      estimatedTokensForRequest: estimatedTokens,
      limit: TOKEN_LIMIT,
      timestamp: new Date().toISOString(),
    });
    return false; // Not enough tokens remaining
  }

  return true; // Enough tokens remaining
}

// Function to update token usage after an API call and check if limit was exceeded
export async function updateTokenUsage(
  userId: string,
  tokensConsumed: number,
): Promise<boolean> {
  // Ensure user data exists and interval reset logic is applied
  getUserRateLimiter(userId);
  const userData = userRateLimiters.get(userId)!;

  // Update total tokens used
  userData.tokensUsed += tokensConsumed;

  // Check if total tokens used exceeds the limit (and log if it does)
  if (userData.tokensUsed > TOKEN_LIMIT) {
    console.warn(`⚠️ Token limit exceeded for user ${userId} after request:`, {
      totalTokensUsed: userData.tokensUsed,
      limit: TOKEN_LIMIT,
      timestamp: new Date().toISOString(),
    });
    // Note: We still return true here as the request already happened.
    // The canProceedWithTokens function is responsible for pre-emptive blocking.
  }

  return true;
}

// Function to calculate tokens used from LLM API response headers
export function calculateTokensUsed(
  usage:
    | { promptTokens: number; completionTokens: number; totalTokens: number }
    | undefined
    | null,
): number {
  if (!usage) return 0;
  if (!usage.totalTokens) return 0;

  return usage.totalTokens;
}
