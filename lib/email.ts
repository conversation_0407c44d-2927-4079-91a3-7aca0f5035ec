import Mailjet from 'node-mailjet';
const mailjet = new Mailjet({
  apiKey: process.env.MAILJET_API_KEY!,
  apiSecret: process.env.MAILJET_API_SECRET!,
});
export async function sendPasswordResetEmail(
  email: string,
  resetToken: string,
) {
  const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/new-password?token=${resetToken}`;
  const data = {
    Messages: [
      {
        From: {
          Email: process.env.MAILJET_SENDER_EMAIL!,
          Name: 'GentleGossip',
        },
        To: [
          {
            Email: email,
          },
        ],
        Subject: 'Reset your password',
        HTMLPart: `
          <h3>Reset Your Password</h3>
          <p>Hello,</p>
          <p>Someone requested a password reset for your account.</p>
          <p>Click the link below to reset your password:</p>
          <p><a href="${resetUrl}">${resetUrl}</a></p>
          <p>If you didn't request this, you can safely ignore this email.</p>
          <p>This link will expire in 1 hour.</p>
        `,
        TextPart: `
          Reset Your Password
          
          Hello,
          
          Someone requested a password reset for your account.
          
          Click the link below to reset your password:
          ${resetUrl}
          
          If you didn't request this, you can safely ignore this email.
          
          This link will expire in 1 hour.
        `,
      },
    ],
  };
  try {
    await mailjet.post('send', { version: 'v3.1' }).request(data);
  } catch (error) {
    console.error('Failed to send reset password email:', error);
    throw new Error('Failed to send reset password email');
  }
}
