export const expressionColors = {
  admiration: '#ffc58f',
  adoration: '#ffc6cc',
  'aesthetic Appreciation': '#e2cbff',
  amusement: '#febf52',
  anger: '#b21816',
  annoyance: '#ffffff',
  anxiety: '#6e42cc',
  awe: '#7dabd3',
  awkwardness: '#d7d99d',
  boredom: '#a4a4a4',
  calmness: '#a9cce1',
  concentration: '#336cff',
  contemplation: '#b0aeef',
  confusion: '#c66a26',
  contempt: '#76842d',
  contentment: '#e5c6b4',
  craving: '#54591c',
  desire: '#ff6b6b',
  determination: '#ff5c00',
  disappointment: '#006c7c',
  disapproval: '#ffffff',
  disgust: '#1a7a41',
  distress: '#c5f264',
  doubt: '#998644',
  ecstasy: '#ff48a4',
  embarrassment: '#63c653',
  'empathic Pain': '#ca5555',
  enthusiasm: '#ffffff',
  entrancement: '#7554d6',
  envy: '#1d4921',
  excitement: '#fff974',
  fear: '#d1c9ef',
  gratitude: '#ffffff',
  guilt: '#879aa1',
  horror: '#772e7a',
  interest: '#a9cce1',
  joy: '#ffd600',
  love: '#f44f4c',
  neutral: '#879aa1',
  nostalgia: '#b087a1',
  pain: '#8c1d1d',
  pride: '#9a4cb6',
  realization: '#217aa8',
  relief: '#fe927a',
  romance: '#f0cc86',
  sadness: '#305575',
  sarcasm: '#ffffff',
  satisfaction: '#a6ddaf',
  'sexual Desire': '#aa0d59',
  shame: '#8a6262',
  surprise: '#70e63a',
  surpriseNegative: '#70e63a',
  surprisePositive: '#7affff',
  sympathy: '#7f88e0',
  tiredness: '#757575',
  triumph: '#ec8132',
} as const;

export const isExpressionColor = (
  color: string,
): color is keyof typeof expressionColors => {
  return color in expressionColors;
};
