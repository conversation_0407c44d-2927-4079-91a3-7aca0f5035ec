import { anthropic } from '@ai-sdk/anthropic';
import { openai } from '@ai-sdk/openai';
import { experimental_wrapLanguageModel as wrapLanguageModel } from 'ai';

import { customMiddleware } from './custom-middleware';

export const customModel = (apiIdentifier: string, provider: string) => {
  return wrapLanguageModel({
    model:
      provider === 'anthropic'
        ? anthropic(apiIdentifier)
        : openai(apiIdentifier),
    middleware: customMiddleware,
  });
};
