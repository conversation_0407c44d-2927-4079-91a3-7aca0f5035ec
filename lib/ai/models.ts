// Define your models here.
import { therapyPrompt, friendPrompt, blocksPrompt } from './prompts';
export interface Model {
  id: string;
  label: string;
  apiIdentifier: string;
  provider: string;
  description: string;
  systemPrompt: string;
}

export const models: Array<Model> = [
  {
    id: 'coach',
    label: 'Coach',
    apiIdentifier: 'claude-3-5-sonnet-20241022',
    provider: 'anthropic',
    description: 'Guiding growth with wisdom and clarity',
    systemPrompt: therapyPrompt + '\n',
  },
  {
    id: 'companion',
    label: 'Companion',
    apiIdentifier: 'gpt-4o',
    provider: 'openai',
    description: 'Your supportive and caring digital companion',
    systemPrompt: friendPrompt + '\n',
  },
] as const;

export const DEFAULT_MODEL_NAME: string = 'coach';
