import type { Experimental_LanguageModelV1Middleware } from 'ai';

// Create a middleware for caching prompt responses using <PERSON>'s cache_control
export const promptCacheMiddleware: Experimental_LanguageModelV1Middleware = {
  // This wraps the generate method (used for non-streaming responses)
  wrapGenerate: async ({ doGenerate, params }) => {
    // Always apply cache_control regardless of model
    // Create a modified params object for Anthropic's cache_control
    const paramsWithCache = { ...params } as any;

    // Prepare system prompt for caching
    // Check for system prompt in different possible locations
    const systemPrompt =
      paramsWithCache.system ||
      (paramsWithCache.prompt && paramsWithCache.prompt.system);

    if (systemPrompt) {
      // Handle system prompt formatting for cache_control
      if (typeof systemPrompt === 'string') {
        paramsWithCache.system = [
          {
            type: 'text',
            text: systemPrompt,
            cache_control: { type: 'ephemeral' },
          },
        ];
      } else if (Array.isArray(systemPrompt)) {
        paramsWithCache.system = systemPrompt.map((item) => ({
          ...item,
          cache_control: { type: 'ephemeral' },
        }));
      }

      // Create a function that will use our modified params
      const doGenerateWithCache = () => {
        // Replace original params with our modified version temporarily
        const originalParams = { ...params };
        Object.assign(params, paramsWithCache);

        // Call the original generate function
        const result = doGenerate();

        // Restore original params to avoid side effects
        Object.assign(params, originalParams);

        return result;
      };

      // Use the wrapped function with cache_control
      return await doGenerateWithCache();
    }

    // If no system content to add cache control to
    return await doGenerate();
  },

  // This wraps the stream method (used for streaming responses)
  wrapStream: async ({ doStream, params }) => {
    // Create a modified params object for Anthropic's cache_control
    const paramsWithCache = { ...params } as any;

    // Check for system prompt in different possible locations
    const systemPrompt =
      paramsWithCache.system ||
      (paramsWithCache.prompt && paramsWithCache.prompt.system);

    if (systemPrompt) {
      // Handle system prompt formatting for cache_control
      if (typeof systemPrompt === 'string') {
        paramsWithCache.system = [
          {
            type: 'text',
            text: systemPrompt,
            cache_control: { type: 'ephemeral' },
          },
        ];
      } else if (Array.isArray(systemPrompt)) {
        paramsWithCache.system = systemPrompt.map((item) => ({
          ...item,
          cache_control: { type: 'ephemeral' },
        }));
      }

      // Create a function that will use our modified params
      const doStreamWithCache = () => {
        // Replace original params with our modified version temporarily
        const originalParams = { ...params };
        Object.assign(params, paramsWithCache);

        // Call the original stream function
        const result = doStream();

        // Restore original params to avoid side effects
        Object.assign(params, originalParams);

        return result;
      };

      // Use the wrapped function with cache_control
      return await doStreamWithCache();
    }

    // If no system content to add cache control to
    return await doStream();
  },
};
