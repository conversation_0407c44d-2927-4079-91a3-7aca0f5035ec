-- Create the daily_analysis_nodes table in the profile schema
CREATE TABLE IF NOT EXISTS profile.daily_analysis_nodes (
    node_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    label VARCHAR(255) NOT NULL,
    node_type VARCHAR(100) NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    first_discovered_date DATE,
    last_seen_date DAT<PERSON>,
    CONSTRAINT daily_analysis_nodes_user_id_label_node_type_key UNIQUE (user_id, label, node_type),
    FOREIGN KEY (user_id) REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Create the daily_analysis_edges table in the profile schema
CREATE TABLE IF NOT EXISTS profile.daily_analysis_edges (
    edge_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    source_node_id UUID NOT NULL,
    target_node_id UUID NOT NULL,
    intensity NUMERIC(3, 2) NOT NULL,
    valence NUMERIC(3, 2) NOT NULL,
    variance NUMERIC(3, 2) NOT NULL,
    summary TEXT,
    analysis_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_daily_analysis_intensity_range CHECK ((intensity >= 0::numeric) AND (intensity <= 1::numeric)),
    CONSTRAINT check_daily_analysis_valence_range CHECK ((valence >= -1::numeric) AND (valence <= 1::numeric)),
    CONSTRAINT check_daily_analysis_variance_range CHECK ((variance >= 0::numeric) AND (variance <= 1::numeric)),
    CONSTRAINT daily_analysis_edges_user_source_target_date_key UNIQUE (user_id, source_node_id, target_node_id, analysis_date),
    FOREIGN KEY (source_node_id) REFERENCES profile.daily_analysis_nodes(node_id) ON DELETE CASCADE,
    FOREIGN KEY (target_node_id) REFERENCES profile.daily_analysis_nodes(node_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES public."User"(id) ON DELETE CASCADE
);

-- Create indexes for daily_analysis_nodes
CREATE INDEX IF NOT EXISTS idx_daily_analysis_nodes_dates ON profile.daily_analysis_nodes(first_discovered_date, last_seen_date);
CREATE INDEX IF NOT EXISTS idx_daily_analysis_nodes_node_type ON profile.daily_analysis_nodes(node_type);

-- Create indexes for daily_analysis_edges
CREATE INDEX IF NOT EXISTS idx_daily_analysis_edges_analysis_date ON profile.daily_analysis_edges(analysis_date);
CREATE INDEX IF NOT EXISTS idx_daily_analysis_edges_composite ON profile.daily_analysis_edges(user_id, source_node_id, target_node_id);
CREATE INDEX IF NOT EXISTS idx_daily_analysis_edges_source_node ON profile.daily_analysis_edges(source_node_id);
CREATE INDEX IF NOT EXISTS idx_daily_analysis_edges_target_node ON profile.daily_analysis_edges(target_node_id);
