-- Create the profile schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS profile;

-- Create the batches table
CREATE TABLE IF NOT EXISTS profile.batches (
    batch_id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    last_message_ts TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    title VARCHAR(200),
    summary TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    messages_processed INTEGER NOT NULL DEFAULT 0,
    first_message_id UUID,
    last_message_id UUID,
    FOREIGN KEY (user_id) REFERENCES public."User"(id)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_batches_user_id ON profile.batches(user_id);
CREATE INDEX IF NOT EXISTS idx_batches_last_message_ts ON profile.batches(last_message_ts);

-- Add a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION profile.update_updated_at_column()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_batches_updated_at
    BEFORE UPDATE ON profile.batches
    FOR EACH ROW
    EXECUTE FUNCTION profile.update_updated_at_column(); 