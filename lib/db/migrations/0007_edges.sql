-- Create the edges table in the profile schema
CREATE TABLE IF NOT EXISTS profile.edges (
    edge_id UUID PRIMARY KEY,
    batch_id UUID NOT NULL,
    user_id UUID NOT NULL,
    from_node_id UUID NOT NULL,
    to_node_id UUID NOT NULL,
    intensity TEXT,
    valence TEXT,
    variance TEXT,
    timeframe_start TIMESTAMP,
    timeframe_end TIMESTAMP,
    summary TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_id) REFERENCES profile.batches(batch_id),
    FOREIGN KEY (from_node_id) REFERENCES profile.nodes(node_id),
    FOREIGN KEY (to_node_id) REFERENCES profile.nodes(node_id),
    FOREIGN KEY (user_id) REFERENCES public."User"(id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_edges_from_node ON profile.edges(from_node_id);
CREATE INDEX IF NOT EXISTS idx_edges_to_node ON profile.edges(to_node_id);
CREATE INDEX IF NOT EXISTS idx_edges_user_id ON profile.edges(user_id);
CREATE INDEX IF NOT EXISTS idx_edges_batch_id ON profile.edges(batch_id);

-- Add a trigger to update the updated_at timestamp
CREATE TRIGGER update_edges_updated_at
    BEFORE UPDATE ON profile.edges
    FOR EACH ROW
    EXECUTE FUNCTION profile.update_updated_at_column(); 