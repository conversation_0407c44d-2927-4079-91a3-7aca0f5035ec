CREATE TABLE IF NOT EXISTS "DebugMessage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatId" uuid NOT NULL,
	"role" varchar NOT NULL,
	"content" json NOT NULL,
	"createdAt" timestamp NOT NULL,
	"modelId" varchar,
	"systemPromptId" varchar
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DebugMessage" ADD CONSTRAINT "DebugMessage_chatId_Chat_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."Chat"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$; 