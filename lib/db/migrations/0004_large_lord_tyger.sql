CREATE TABLE IF NOT EXISTS "RefreshToken" (
	"token" text PRIMARY KEY NOT NULL,
	"user_id" uuid,
	"session_id" uuid NOT NULL,
	"device_info" text,
	"expires_at" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"last_used_at" timestamp NOT NULL,
	CONSTRAINT "RefreshToken_session_id_unique" UNIQUE("session_id")
);
--> statement-breakpoint
ALTER TABLE "Chat" ADD COLUMN "deleted" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "Chat" ADD COLUMN "isVoiceChat" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "Message" ADD COLUMN "modelId" varchar;--> statement-breakpoint
ALTER TABLE "Message" ADD COLUMN "systemPromptId" varchar;--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "reset_token" text;--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "reset_token_expires" timestamp;--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "is_temporary" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "google_id" varchar(64);--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "google_access_token" text;--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "google_refresh_token" text;--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "google_token_expiry" timestamp;--> statement-breakpoint
ALTER TABLE "User" ADD COLUMN "created_at" timestamp DEFAULT now() NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "RefreshToken" ADD CONSTRAINT "RefreshToken_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "User" ADD CONSTRAINT "User_reset_token_unique" UNIQUE("reset_token");--> statement-breakpoint
ALTER TABLE "User" ADD CONSTRAINT "User_google_id_unique" UNIQUE("google_id");