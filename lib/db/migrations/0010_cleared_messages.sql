CREATE TABLE IF NOT EXISTS "public"."ClearedMessages" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"last_cleared_message_id" uuid NOT NULL,
	"cleared_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "public"."ClearedMessages" ADD CONSTRAINT "ClearedMessages_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "public"."ClearedMessages" ADD CONSTRAINT "ClearedMessages_last_cleared_message_id_Message_id_fk" FOREIGN KEY ("last_cleared_message_id") REFERENCES "public"."Message"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_cleared_messages_user_id" ON "public"."ClearedMessages" ("user_id");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_cleared_messages_cleared_at" ON "public"."ClearedMessages" ("cleared_at");
