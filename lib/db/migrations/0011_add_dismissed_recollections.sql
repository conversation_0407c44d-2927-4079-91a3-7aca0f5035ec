-- Ensure profile schema exists (should already exist from migration 0006)
DO $$ BEGIN
    CREATE SCHEMA IF NOT EXISTS profile;
EXCEPTION
    WHEN duplicate_schema THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "profile"."dismissed_recollections" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"recollection_id" uuid NOT NULL,
	"dismissed_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "dismissed_recollections_user_id_recollection_id_unique" UNIQUE("user_id","recollection_id")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "profile"."dismissed_recollections" ADD CONSTRAINT "dismissed_recollections_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "profile"."dismissed_recollections" ADD CONSTRAINT "dismissed_recollections_recollection_id_recollections_recollection_id_fk" FOREIGN KEY ("recollection_id") REFERENCES "profile"."recollections"("recollection_id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_dismissed_recollections_user_id" ON "profile"."dismissed_recollections" ("user_id");
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_dismissed_recollections_recollection_id" ON "profile"."dismissed_recollections" ("recollection_id");
