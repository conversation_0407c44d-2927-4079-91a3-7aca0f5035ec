/**
 * Client-side retry utility for handling message sending failures
 */

export interface RetryOptions {
  maxRetries?: number;
  initialDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  retryCondition?: (error: Error) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
  timeout?: number;
}

export interface RetryState {
  isRetrying: boolean;
  retryCount: number;
  lastError?: Error;
  maxRetries: number;
}

const DEFAULT_RETRY_OPTIONS: Required<RetryOptions> = {
  maxRetries: 3,
  initialDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: () => true,
  onRetry: () => {},
  timeout: 30000, // 30 seconds timeout
};

/**
 * Determines if an error should trigger a retry
 */
export function shouldRetryError(error: Error): boolean {
  // Network errors
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return true;
  }
  
  // Network connectivity issues
  if (error.message.includes('NetworkError') || 
      error.message.includes('Failed to fetch') ||
      error.message.includes('ERR_NETWORK') ||
      error.message.includes('ERR_INTERNET_DISCONNECTED')) {
    return true;
  }
  
  // Timeout errors
  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return true;
  }
  
  // Server errors (5xx)
  if (error.message.includes('500') || 
      error.message.includes('502') || 
      error.message.includes('503') || 
      error.message.includes('504')) {
    return true;
  }
  
  // Rate limiting (429) - should retry with backoff
  if (error.message.includes('429') || error.message.includes('rate limit')) {
    return true;
  }
  
  // Generic server errors
  if (error.message.includes('Internal Server Error') ||
      error.message.includes('Service Unavailable') ||
      error.message.includes('Bad Gateway') ||
      error.message.includes('Gateway Timeout')) {
    return true;
  }
  
  return false;
}

/**
 * Creates a delay promise for exponential backoff
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Calculates the next delay using exponential backoff with jitter
 */
function calculateDelay(attempt: number, options: Required<RetryOptions>): number {
  const exponentialDelay = options.initialDelay * Math.pow(options.backoffFactor, attempt - 1);
  const delayWithJitter = exponentialDelay + (Math.random() * 1000); // Add up to 1s jitter
  return Math.min(delayWithJitter, options.maxDelay);
}

/**
 * Wraps a promise with a timeout
 */
function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
    })
  ]);
}

/**
 * Retry utility function with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const config = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: Error;
  
  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      // Wrap the operation with timeout
      const result = await withTimeout(operation(), config.timeout);
      return result;
    } catch (error) {
      lastError = error as Error;
      
      // If this is the last attempt, throw the error
      if (attempt > config.maxRetries) {
        throw lastError;
      }
      
      // Check if we should retry this error
      if (!config.retryCondition(lastError)) {
        throw lastError;
      }
      
      // Call the retry callback
      config.onRetry(lastError, attempt);
      
      // Calculate delay and wait
      const delayMs = calculateDelay(attempt, config);
      await delay(delayMs);
    }
  }
  
  throw lastError!;
}

/**
 * Creates a retry state object for tracking retry attempts
 */
export function createRetryState(maxRetries: number = DEFAULT_RETRY_OPTIONS.maxRetries): RetryState {
  return {
    isRetrying: false,
    retryCount: 0,
    lastError: undefined,
    maxRetries,
  };
}

/**
 * Updates retry state for a new attempt
 */
export function updateRetryState(
  state: RetryState, 
  isRetrying: boolean, 
  error?: Error
): RetryState {
  return {
    ...state,
    isRetrying,
    retryCount: isRetrying ? state.retryCount + 1 : 0,
    lastError: error,
  };
}

/**
 * Checks if retry state indicates we should give up
 */
export function shouldGiveUpRetry(state: RetryState): boolean {
  return state.retryCount >= state.maxRetries;
}
