import { useMemo, useRef } from 'react';
import useS<PERSON> from 'swr';
import type { Message } from 'ai';
import type { Vote } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';

/**
 * Custom hook for managing vote fetching with optimized caching
 * Only refetches when assistant message IDs actually change
 */
export function useVotes(messages: Message[]) {
  // Keep track of the last assistant message IDs to prevent unnecessary refetches
  const lastAssistantMessageIdsRef = useRef<string>('');

  // Memoize assistant message IDs and only recalculate when they actually change
  const assistantMessageIds = useMemo(() => {
    const ids = messages
      .filter(msg => msg.role === 'assistant')
      .map(msg => msg.id)
      .filter(id => id); // Filter out any undefined IDs
    
    const idsString = ids.join(',');
    
    // Only update if the IDs actually changed
    if (idsString !== lastAssistantMessageIdsRef.current) {
      lastAssistantMessageIdsRef.current = idsString;
    }
    
    return ids;
  }, [messages.filter(msg => msg.role === 'assistant').map(msg => msg.id).join(',')]);

  // Use the stable reference for SWR key to prevent unnecessary refetches
  const swrKey = assistantMessageIds.length > 0 
    ? `/api/vote?messageIds=${lastAssistantMessageIdsRef.current}` 
    : null;

  const { data: votes, mutate } = useSWR<Array<Vote>>(
    swrKey,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // Only refetch at most every 60 seconds
      // Add compare function to prevent refetch on identical data
      compare: (a, b) => {
        if (!a && !b) return true;
        if (!a || !b) return false;
        if (a.length !== b.length) return false;
        return a.every((vote, index) => 
          vote.messageId === b[index]?.messageId && 
          vote.isUpvoted === b[index]?.isUpvoted
        );
      }
    },
  );

  // Helper function to invalidate votes for a specific message
  const invalidateVoteForMessage = (messageId: string) => {
    mutate((currentVotes) => {
      // If we don't have current votes, just trigger a refetch
      if (!currentVotes) return undefined;
      
      // Return undefined to trigger a refetch only if this message has votes
      const hasVoteForMessage = currentVotes.some(vote => vote.messageId === messageId);
      return hasVoteForMessage ? undefined : currentVotes;
    });
  };

  return {
    votes,
    invalidateVoteForMessage,
    mutate
  };
}
