#!/usr/bin/env node

/**
 * Test script to verify retry functionality
 * This script sends a message to the chat API to trigger retry logic
 */

const BASE_URL = 'http://localhost:3001';
const TEST_CHAT_ID = '339add87-c120-481d-9fd7-4a74afa493db'; // From seed data

async function testRetryFunctionality() {
  console.log('🧪 Testing Retry Functionality...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📝 Test Chat ID: ${TEST_CHAT_ID}`);
  
  const testMessage = {
    id: TEST_CHAT_ID,
    messages: [
      {
        id: 'test-message-' + Date.now(),
        role: 'user',
        content: 'Hello, this is a test message to trigger retry functionality.'
      }
    ],
    modelId: 'coach'
  };

  try {
    console.log('📤 Sending test message...');
    
    const response = await fetch(`${BASE_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testMessage),
    });

    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📊 Response OK: ${response.ok}`);
    
    if (response.ok) {
      console.log('✅ Request succeeded');
      
      // Try to read the streaming response
      const reader = response.body?.getReader();
      if (reader) {
        console.log('📖 Reading streaming response...');
        
        let retryAttempts = 0;
        let successReceived = false;
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = new TextDecoder().decode(value);
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.type === 'retry-attempt') {
                  retryAttempts++;
                  console.log(`🔄 Retry attempt ${data.content.attempt} detected:`, data.content);
                }
                
                if (data.type === 'retry-success') {
                  successReceived = true;
                  console.log('✅ Retry success signal received:', data.content);
                }
              } catch (e) {
                // Ignore JSON parse errors for non-JSON chunks
              }
            }
          }
        }
        
        console.log(`📊 Total retry attempts detected: ${retryAttempts}`);
        console.log(`📊 Success signal received: ${successReceived}`);
        
        if (retryAttempts > 0) {
          console.log('🎉 Retry functionality is working! Retry attempts were detected.');
        } else {
          console.log('⚠️ No retry attempts detected. This could mean:');
          console.log('   - Retry testing is not enabled');
          console.log('   - The request succeeded on first attempt');
          console.log('   - Retry data is not being sent to client');
        }
      }
    } else {
      console.error('❌ Request failed:', response.statusText);
      const errorText = await response.text();
      console.error('Error details:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testRetryFunctionality().catch(console.error);
