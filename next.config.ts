import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: '/new',
        destination: '/',
        permanent: true,
      },
      {
        source: '/tos.html',
        destination: '/terms.html',
        permanent: true,
      },
    ];
  },
};
// const withBundleAnalyzer = require('@next/bundle-analyzer')({
//   enabled: process.env.ANALYZE === 'true',
// });

// module.exports = withBundleAnalyzer(nextConfig);
export default nextConfig;
