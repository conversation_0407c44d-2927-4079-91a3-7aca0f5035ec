/* Basic editor styles */
.ProseMirror {
  min-height: 300px;
  padding: 1rem;
}

.ProseMirror:focus {
  outline: none;
}

.ProseMirror > * + * {
  margin-top: 0.75em;
}

.ProseMirror ul,
.ProseMirror ol {
  padding: 0 1rem;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  line-height: 1.1;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.ProseMirror h1 {
  font-size: 1.75rem;
  font-weight: 600;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

.ProseMirror code {
  background-color: rgba(97, 97, 97, 0.1);
  color: #616161;
  padding: 0.25rem;
  border-radius: 0.25rem;
  font-family: 'JetBrainsMono', monospace;
  font-size: 0.875rem;
}

.ProseMirror pre {
  background: #0d0d0d;
  color: #fff;
  font-family: 'JetBrainsMono', monospace;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.ProseMirror pre code {
  color: inherit;
  padding: 0;
  background: none;
  font-size: 0.875rem;
}

.ProseMirror blockquote {
  padding-left: 1rem;
  border-left: 2px solid rgba(13, 13, 13, 0.1);
  margin-left: 0;
  margin-right: 0;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid rgba(13, 13, 13, 0.1);
  margin: 2rem 0;
}

/* Dark mode adjustments */
.dark .ProseMirror code {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
}

.dark .ProseMirror blockquote {
  border-left-color: rgba(255, 255, 255, 0.2);
}

.dark .ProseMirror hr {
  border-top-color: rgba(255, 255, 255, 0.2);
}

.dark .ProseMirror pre {
  background-color: rgba(0, 0, 0, 0.4);
}

/* Tight lists */
.ProseMirror ul.tight li p,
.ProseMirror ol.tight li p {
  margin: 0;
}
