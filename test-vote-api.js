#!/usr/bin/env node

/**
 * Simple test script to verify the voting API functionality
 * Run with: node test-vote-api.js
 */

const BASE_URL = 'http://localhost:3001';

// Test data - using a known chat ID from seed data
const testChatId = '339add87-c120-481d-9fd7-4a74afa493db'; // From seed data
const testMessageId = '550e8400-e29b-41d4-a716-446655440000'; // Mock message ID
const nonExistentChatId = '00000000-0000-0000-0000-000000000000'; // Non-existent chat ID

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    const text = await response.text();
    let data;
    try {
      data = JSON.parse(text);
    } catch {
      data = text;
    }
    
    return {
      status: response.status,
      ok: response.ok,
      data,
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message,
    };
  }
}

async function testGetVotes() {
  console.log('\n🧪 Testing GET /api/vote...');
  
  // Test 1: Valid chatId
  console.log('  ✓ Testing with valid chatId...');
  const result1 = await makeRequest(`${BASE_URL}/api/vote?chatId=${testChatId}`);
  console.log(`    Status: ${result1.status}, Data:`, result1.data);
  
  // Test 2: Missing chatId
  console.log('  ✓ Testing with missing chatId...');
  const result2 = await makeRequest(`${BASE_URL}/api/vote`);
  console.log(`    Status: ${result2.status}, Data:`, result2.data);
  
  // Test 3: Invalid chatId format
  console.log('  ✓ Testing with invalid chatId format...');
  const result3 = await makeRequest(`${BASE_URL}/api/vote?chatId=invalid-uuid`);
  console.log(`    Status: ${result3.status}, Data:`, result3.data);
  
  return result1.ok && result1.status === 200;
}

async function testVoteMessage() {
  console.log('\n🧪 Testing PATCH /api/vote...');

  // Test 1: Non-existent chat
  console.log('  ✓ Testing with non-existent chat...');
  const nonExistentChatResult = await makeRequest(`${BASE_URL}/api/vote`, {
    method: 'PATCH',
    body: JSON.stringify({
      chatId: nonExistentChatId,
      messageId: testMessageId,
      type: 'up',
    }),
  });
  console.log(`    Status: ${nonExistentChatResult.status}, Data:`, nonExistentChatResult.data);

  // Test 2: Missing required fields
  console.log('  ✓ Testing with missing fields...');
  const missingFieldsResult = await makeRequest(`${BASE_URL}/api/vote`, {
    method: 'PATCH',
    body: JSON.stringify({
      chatId: testChatId,
      // missing messageId and type
    }),
  });
  console.log(`    Status: ${missingFieldsResult.status}, Data:`, missingFieldsResult.data);

  // Test 3: Invalid UUID format
  console.log('  ✓ Testing with invalid UUID format...');
  const invalidUuidResult = await makeRequest(`${BASE_URL}/api/vote`, {
    method: 'PATCH',
    body: JSON.stringify({
      chatId: 'invalid-uuid',
      messageId: testMessageId,
      type: 'up',
    }),
  });
  console.log(`    Status: ${invalidUuidResult.status}, Data:`, invalidUuidResult.data);

  // Test 4: Valid vote (if we can find a real message)
  console.log('  ✓ Testing with potentially valid data...');
  const validVoteResult = await makeRequest(`${BASE_URL}/api/vote`, {
    method: 'PATCH',
    body: JSON.stringify({
      chatId: testChatId,
      messageId: testMessageId,
      type: 'up',
    }),
  });
  console.log(`    Status: ${validVoteResult.status}, Data:`, validVoteResult.data);

  return missingFieldsResult.status === 400 && invalidUuidResult.status === 400;
}

async function testVotePersistence() {
  console.log('\n🧪 Testing vote persistence...');
  
  // Create a vote
  await makeRequest(`${BASE_URL}/api/vote`, {
    method: 'PATCH',
    body: JSON.stringify({
      chatId: testChatId,
      messageId: testMessageId,
      type: 'up',
    }),
  });
  
  // Retrieve votes and check if our vote exists
  const result = await makeRequest(`${BASE_URL}/api/vote?chatId=${testChatId}`);
  console.log('  ✓ Retrieved votes:', result.data);
  
  if (Array.isArray(result.data)) {
    const ourVote = result.data.find(vote => 
      vote.messageId === testMessageId && vote.chatId === testChatId
    );
    console.log('  ✓ Our vote found:', ourVote);
    return !!ourVote && ourVote.isUpvoted === true;
  }
  
  return false;
}

async function runTests() {
  console.log('🚀 Starting Vote API Tests...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📝 Test Chat ID: ${testChatId}`);
  console.log(`📝 Test Message ID: ${testMessageId}`);
  
  const results = {
    getVotes: await testGetVotes(),
    voteMessage: await testVoteMessage(),
    persistence: await testVotePersistence(),
  };
  
  console.log('\n📊 Test Results:');
  console.log('  GET /api/vote:', results.getVotes ? '✅ PASS' : '❌ FAIL');
  console.log('  PATCH /api/vote:', results.voteMessage ? '✅ PASS' : '❌ FAIL');
  console.log('  Vote Persistence:', results.persistence ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = Object.values(results).every(result => result);
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  return allPassed;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
