# Testing Retry Functionality and Visual Indicators

This document explains how to test the retry logic and visual indicators for Anthropic API calls.

## 🧪 Testing Methods

### Method 1: Environment Variable (Recommended)

Set the `TEST_RETRY_LOGIC` environment variable:

```bash
# Fail first 2 attempts, then succeed
TEST_RETRY_LOGIC=true npm run dev

# Random 50% failure rate per attempt
TEST_RETRY_LOGIC=random npm run dev

# Simulate timeout errors for first 2 attempts
TEST_RETRY_LOGIC=timeout npm run dev

# Disable testing (default)
TEST_RETRY_LOGIC=false npm run dev
```

### Method 2: Code Modification

In `app/(chat)/api/chat/route.ts`, change:
```typescript
const ENABLE_RETRY_TESTING = false; // Set to true
```

### Method 3: Query Parameter (Quick Testing)

Add `?testRetry=true` to any chat URL:
```
http://localhost:3000/chat/some-id?testRetry=true
```

## 🎯 What You'll See When Testing

### Visual Indicators (Client-Side Detection)
1. **Retry Indicator**: Top-right component showing:
   - Appears after 3 seconds of loading
   - Simulated attempt progress (increments every 4 seconds)
   - Countdown timer with progress bar
   - Shows "Processing Request" or "Retrying Request"
2. **Enhanced Loading States**: Input area shows spinner with enhanced styling
3. **Stop Button**: Red-tinted stop button during retries

### How It Works
- **Client-Side Retry Logic**: Automatically retries failed `/api/chat` requests
- **Custom Fetch Wrapper**: Intercepts and retries network failures, timeouts, and server errors
- **Real-time Visual Feedback**: Shows retry indicator immediately when retries begin
- **Exponential Backoff**: 2s, 4s, 8s delays between retry attempts
- **Max 3 Retries**: Gives up after 3 failed attempts to avoid infinite loops

### Console Output
```
💥 [TESTING] Simulating API failure on attempt 1
🔄 Retrying API call (attempt 1): Simulated API Error - Testing Retry Logic (Attempt 1)
💥 [TESTING] Simulating API failure on attempt 2
🔄 Retrying API call (attempt 2): Simulated API Error - Testing Retry Logic (Attempt 2)
✅ [TESTING] Allowing success on attempt 3
```

## 🔧 Testing Scenarios

### Default Testing (`TEST_RETRY_LOGIC=true`)
- First 2 attempts fail with simulated errors
- 3rd attempt succeeds
- Shows full retry flow with visual indicators

### Random Testing (`TEST_RETRY_LOGIC=random`)
- Each attempt has 50% chance of failure
- Good for testing variable retry scenarios
- May succeed immediately or take several attempts

### Timeout Testing (`TEST_RETRY_LOGIC=timeout`)
- First 2 attempts fail with timeout errors
- 3rd attempt succeeds
- Tests timeout-specific error handling

## 📝 Implementation Details

### Retry Configuration
- **Max Retries**: 7 attempts
- **Backoff Factor**: 3x (exponential backoff)
- **Min Timeout**: 1000ms
- **Max Timeout**: 60000ms
- **Randomization**: Disabled for predictable testing

### Error Types Simulated
- Generic API errors
- Timeout errors
- Rate limiting errors (separate from retry testing)

## 🚀 How to Use for Development

1. **Enable testing** using any method above
2. **Start the development server**
3. **Send a chat message** 
4. **Observe the retry behavior**:
   - Watch toast notifications appear
   - See retry indicator in top-right
   - Notice enhanced loading states
   - Check console for detailed logs

## 🔄 Disabling Testing

To disable testing:
- Set `ENABLE_RETRY_TESTING = false` in code
- Remove `TEST_RETRY_LOGIC` environment variable
- Don't use `?testRetry=true` query parameter

## 📋 Testing Checklist

- [ ] Retry indicator shows in top-right corner
- [ ] Progress bar counts down correctly
- [ ] Error messages are displayed
- [ ] Loading states work in input area
- [ ] Stop button is available during retries
- [ ] Console logs show retry attempts
- [ ] Final attempt succeeds and UI returns to normal
- [ ] Multiple consecutive requests work correctly

## 🐛 Troubleshooting

If testing doesn't work:
1. Check console for error messages
2. Verify environment variable is set correctly
3. Ensure you're sending a chat message (not just loading the page)
4. Check that the retry testing code is uncommented in the API route
5. Verify the streaming data is being processed correctly in the frontend
