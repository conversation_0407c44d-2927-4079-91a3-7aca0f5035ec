'use server';

import { hash } from 'bcryptjs';
import { randomBytes } from 'node:crypto';
import { z } from 'zod';

import { signIn } from './auth';
import {
  createUser,
  getUser,
  getUserByEmail,
  getUserByResetToken,
  updateUserResetToken,
  updateUserPassword,
  updateTemporaryUser,
} from '@/lib/db/queries';
import { sendPasswordResetEmail } from '@/lib/email';
import { getTempUserId, deleteTempUserCookie } from '@/app/actions/cookies';

const authFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export interface LoginActionState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data';
}

export const login = async (formData: FormData): Promise<LoginActionState> => {
  try {
    const validatedData = authFormSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
    });

    const [dbUser] = await getUser(validatedData.email);
    if (!dbUser) {
      return { status: 'failed' };
    }

    const signInResult = await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
      callbackUrl: '/',
    });

    if (signInResult?.error) {
      return { status: 'failed' };
    }

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    return { status: 'failed' };
  }
};

export interface RegisterActionState {
  status:
    | 'idle'
    | 'in_progress'
    | 'success'
    | 'failed'
    | 'user_exists'
    | 'invalid_data';
}

export const register = async (
  _: RegisterActionState,
  formData: FormData,
): Promise<RegisterActionState> => {
  try {
    const validatedData = authFormSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
    });

    // Check for existing permanent user
    const [existingUser] = await getUser(validatedData.email);
    if (existingUser && !existingUser.is_temporary) {
      return { status: 'user_exists' };
    }

    const plainPassword = validatedData.password;
    const hashedPassword = await hash(plainPassword, 10);

    // Get temp user ID from cookie if it exists
    const tempUserId = await getTempUserId();

    if (tempUserId) {
      // Update the temporary user instead of creating a new one
      await updateTemporaryUser({
        id: tempUserId,
        email: validatedData.email,
        password: hashedPassword,
      });

      // Clear the temporary user cookie
      await deleteTempUserCookie();
    } else {
      // Create new user if no temporary user exists
      await createUser(validatedData.email, plainPassword);
    }

    const signInResult = await signIn('credentials', {
      email: validatedData.email,
      password: plainPassword,
      redirect: false,
      callbackUrl: '/',
    });

    if (signInResult?.error) {
      return { status: 'failed' };
    }

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    return { status: 'failed' };
  }
};

export interface ResetPasswordActionState {
  status:
    | 'idle'
    | 'in_progress'
    | 'success'
    | 'failed'
    | 'not_found'
    | 'invalid_data';
}

export const resetPassword = async (
  formData: FormData,
): Promise<ResetPasswordActionState> => {
  try {
    const validatedData = z
      .object({
        email: z.string().email(),
      })
      .parse({
        email: formData.get('email'),
      });

    const user = await getUserByEmail(validatedData.email);

    if (!user) {
      return { status: 'not_found' };
    }
    console.log('USER FOUND HERE!', user);
    // Generate reset token
    const resetToken = randomBytes(32).toString('hex');
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour from now

    // Save token to database
    await updateUserResetToken(user.id, resetToken, resetTokenExpires);

    // Send reset email
    await sendPasswordResetEmail(validatedData.email, resetToken);

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    console.error('Reset password error:', error);
    return { status: 'failed' };
  }
};

export interface ConfirmResetPasswordActionState {
  status:
    | 'idle'
    | 'in_progress'
    | 'success'
    | 'failed'
    | 'invalid_token'
    | 'invalid_data';
}

export const confirmResetPassword = async (
  _: ConfirmResetPasswordActionState,
  formData: FormData,
): Promise<ConfirmResetPasswordActionState> => {
  try {
    const validatedData = z
      .object({
        token: z.string(),
        password: z.string().min(8),
      })
      .parse({
        token: formData.get('token'),
        password: formData.get('password'),
      });

    const user = await getUserByResetToken(validatedData.token);

    if (!user) {
      return { status: 'invalid_token' };
    }

    // Hash new password and update user
    const hashedPassword = await hash(validatedData.password, 10);
    await updateUserPassword(user.id, hashedPassword);

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    return { status: 'failed' };
  }
};
