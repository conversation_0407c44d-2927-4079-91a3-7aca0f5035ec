'use client';

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

import { confirmResetPassword } from '@/app/(auth)/actions';
import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';
import { useActionState } from '../hooks/useActionState';

interface ConfirmResetPasswordActionState {
  status:
    | 'idle'
    | 'in_progress'
    | 'success'
    | 'failed'
    | 'invalid_token'
    | 'invalid_data';
}

function NewPasswordForm() {
  const router = useRouter();
  const [password, setPassword] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const [state, formAction] = useActionState<
    ConfirmResetPasswordActionState,
    FormData
  >(
    async (formData: FormData): Promise<ConfirmResetPasswordActionState> =>
      confirmResetPassword(state, formData),
    {
      status: 'idle',
    },
  );

  useEffect(() => {
    if (state.status === 'invalid_token') {
      toast.error('Invalid or expired reset token!');
    } else if (state.status === 'failed') {
      toast.error('Failed to reset password!');
    } else if (state.status === 'invalid_data') {
      toast.error('Password must be at least 8 characters!');
    } else if (state.status === 'success') {
      setIsSuccessful(true);
      toast.success('Password reset successfully!');
      router.push('/login');
    }
  }, [state.status, router]);

  const handleSubmit = async (formData: FormData) => {
    if (!token) {
      toast.error('Invalid reset token!');
      return;
    }
    formData.append('token', token);
    setPassword(formData.get('password') as string);
    formAction(formData);
  };

  return (
    <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
      <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
        <h3 className="text-xl font-semibold dark:text-zinc-50">
          Set New Password
        </h3>
        <p className="text-sm text-gray-500 dark:text-zinc-400">
          Enter your new password below
        </p>
      </div>
      <AuthForm action={handleSubmit} hideEmail>
        <SubmitButton isSuccessful={isSuccessful}>Reset Password</SubmitButton>
      </AuthForm>
    </div>
  );
}

export default function Page() {
  return (
    <div className="flex h-screen w-screen items-center justify-center bg-background">
      <Suspense fallback={<div>Loading...</div>}>
        <NewPasswordForm />
      </Suspense>
    </div>
  );
}
