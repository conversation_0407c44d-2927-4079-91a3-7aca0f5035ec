import type { ReactNode } from 'react';

interface AuthLayoutProps {
  children: ReactNode;
}

const AuthLayout = ({ children }: AuthLayoutProps) => {
  return (
    <div className="min-h-screen flex flex-col bg-[#e8f6f8]">
      <header className="welcome-header flex flex-col items-center justify-center gap-2 py-8 bg-gradient-to-b from-[#f0f9fa] to-[#e8f6f8] border-b border-[#d5eef2]">
        <svg
          className="welcome-logo size-16"
          viewBox="0 0 363 493"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M116.527 198.168C65.7958 230.82 39.6686 237.973 0.483276 218.066V67.297C0.483265 26.3153 16.1288 -0.100967 57.6274 0.900693C99.1261 1.90235 262.058 0.899444 303.63 0.900693C345.202 0.901942 362.936 24.8481 362.936 67.297V217.491C326.502 238.564 295.141 227.633 248.528 198.168C201.914 168.704 167.259 165.516 116.527 198.168Z"
            fill="#0594A2"
          />
          <path
            d="M0.396524 432.098V295.812C39.4563 313.696 65.8073 307.488 116.44 278C167.172 248.455 201.828 251.339 248.441 278C295.017 304.639 326.452 315.372 362.849 296.367V432.098C362.849 470.507 345.115 492.175 303.543 492.176C261.971 492.177 99.0394 491.27 57.5407 492.176C16.042 493.082 0.396512 469.18 0.396524 432.098Z"
            fill="#97E5EB"
          />
          <path
            d="M116.527 297.004C65.7958 341.515 39.6686 351.266 0.483276 324.128V199.661C39.6686 226.798 65.7958 217.048 116.527 172.536C167.259 128.025 201.914 132.37 248.528 172.536C295.141 212.702 326.502 227.604 362.936 198.877V323.345C326.502 352.072 295.141 337.17 248.528 297.004C201.914 256.838 167.259 252.492 116.527 297.004Z"
            fill="#4AC1C7"
          />
        </svg>
        <h1 className="text-[#0594A2] text-2xl font-medium">GentleGossip</h1>
      </header>

      <main className="flex-1">{children}</main>

      <footer className="footer text-center py-4 text-gray-600 text-sm">
        <p>&copy; 2025 GentleGossip. All rights reserved.</p>
      </footer>
    </div>
  );
};

export default AuthLayout;
