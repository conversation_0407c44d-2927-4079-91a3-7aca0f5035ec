'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { signIn } from 'next-auth/react';
import Image from 'next/image';
import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';

import { login } from '../actions';

export default function Page() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);

  const handleSubmit = async (formData: FormData) => {
    setEmail(formData.get('email') as string);

    try {
      const result = await login(formData);

      if (result.status === 'failed') {
        toast.error('Invalid credentials!');
      } else if (result.status === 'invalid_data') {
        toast.error('Failed validating your submission!');
      } else if (result.status === 'success') {
        setIsSuccessful(true);
        router.push('/');
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signIn('google', { callbackUrl: '/' });
    } catch (error) {
      toast.error('Failed to sign in with Google');
    }
  };

  return (
    <div className="container mx-auto px-4 flex-1 flex items-center justify-center">
      <div className="auth-container w-full max-w-[480px]">
        <div className="auth-form bg-white rounded-[20px] p-8 shadow-sm border border-[#e5f4f6]">
          <AuthForm action={handleSubmit} defaultEmail={email}>
            <SubmitButton isSuccessful={isSuccessful}>
              <div>Login</div>
            </SubmitButton>

            <div className="divider text-center text-gray-400 text-sm">
              or continue with
            </div>

            <button
              type="button"
              onClick={handleGoogleSignIn}
              className="google-btn"
            >
              <Image
                src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.1/icons/google.svg"
                alt="Google"
                width="20"
                height="20"
              />
              Continue with Google
            </button>

            <div className="footer-links text-center mt-6 text-sm text-gray-500 flex flex-col gap-3">
              <p>
                {"Don't have an account? "}
                <Link
                  href="/register"
                  className="text-[#0594A2] hover:underline font-medium"
                >
                  Register
                </Link>
                {' for free.'}
              </p>
              <Link
                href="/reset-password"
                className="text-[#0594A2] hover:underline font-medium"
              >
                Forgot Password?
              </Link>
            </div>
          </AuthForm>
        </div>
      </div>
    </div>
  );
}
