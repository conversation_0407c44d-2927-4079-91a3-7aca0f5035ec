import { auth, signIn } from '@/app/(auth)/auth';
import { type NextRequest, NextResponse } from 'next/server';
import { sign } from 'jsonwebtoken';
import { getUser } from '@/lib/db/queries';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    console.log('Login attempt for email:', email);

    const result = await signIn('credentials', {
      email,
      password,
      redirect: false,
    });

    console.log('Sign in result:', result);
    // result could be undefined or error if authentication fails
    if (!result || result.error) {
      console.log('Authentication failed:', result?.error);
      return NextResponse.json(
        { error: result?.error || 'Authentication failed' },
        { status: 401 },
      );
    }

    // Get the session after successful sign in
    const session = await auth();
    console.log('Session after sign in:', session);

    // Get user information from auth service
    const [user] = await getUser(email);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Generate a JWT token using the authenticated user's ID
    const token = sign({ userId: user.id }, process.env.NEXTAUTH_SECRET!, {
      expiresIn: '30d',
    });

    console.log('Generated token:', token);

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
      },
      token: token,
    });
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
