import { NextResponse } from 'next/server';
import { getUserByEmail, createUser } from '@/lib/db/queries';
import { auth, signIn } from '@/app/(auth)/auth';
import { getToken } from 'next-auth/jwt';

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Check if the user already exists using the query function
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 400 },
      );
    }

    // Create new user using the query function
    await createUser(email, password);

    // Sign in the user after registration
    const signInResult = await signIn('credentials', {
      email,
      password,
      redirect: false,
    });

    if (!signInResult || signInResult.error) {
      return NextResponse.json(
        { error: signInResult?.error || 'Authentication failed' },
        { status: 401 },
      );
    }

    // Get the session and token
    const session = await auth();
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    return NextResponse.json({
      success: true,
      user: session?.user,
      token: token?.sub,
    });
  } catch (error) {
    console.error('[register API]', error);
    return NextResponse.json({ error: 'An error occurred' }, { status: 500 });
  }
}
