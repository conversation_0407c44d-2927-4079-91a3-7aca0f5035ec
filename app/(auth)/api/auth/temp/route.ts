import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { createTemporaryUser } from '@/lib/db/queries';
import { generateUUID } from '@/lib/utils';
import type { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('GET /api/auth/temp route handler');
  const tempUserIdCookie = (await cookies()).get('tempUserId');

  // If cookie somehow already exists, just redirect back
  if (tempUserIdCookie) {
    console.log('Temp user cookie already exists, redirecting to /talk');
    redirect('/talk');
  }

  let actualTempUserId: string | undefined;

  try {
    const newTempId = generateUUID();
    console.log('Generated new temp ID:', newTempId);
    const tempUser = await createTemporaryUser(newTempId);

    if (!tempUser || tempUser.length === 0) {
      throw new Error('Failed to create temporary user in DB.');
    }

    actualTempUserId = tempUser[0].id;
    console.log('Created temp user in DB:', actualTempUserId);

    // Set the cookie
    (await cookies()).set('tempUserId', actualTempUserId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });
    console.log('Set tempUserId cookie');
  } catch (error) {
    console.error('Error in /api/auth/temp:', error);
    // Redirect to an error page ONLY if an error occurred
    redirect('/talk?error=temp_user_failed');
  }

  // If we successfully created the user and set the cookie, redirect now.
  // This runs only if the try block succeeded without throwing an error.
  if (actualTempUserId) {
    redirect('/talk');
  } else {
    // Fallback redirect in case something unexpected happened
    // without throwing an error (should ideally not be reached).
    console.error(
      'Reached unexpected state in /api/auth/temp, redirecting with error.',
    );
    redirect('/talk?error=temp_user_unexpected');
  }
}
