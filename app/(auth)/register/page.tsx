'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { signIn } from 'next-auth/react';
import Image from 'next/image';
import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';

import { register, type RegisterActionState } from '../actions';

export default function Page() {
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [state, setState] = useState<RegisterActionState>({ status: 'idle' });

  useEffect(() => {
    if (state.status === 'user_exists') {
      toast.error('Account already exists');
    } else if (state.status === 'failed') {
      toast.error('Failed to create account');
    } else if (state.status === 'invalid_data') {
      toast.error('Failed validating your submission!');
    } else if (state.status === 'success') {
      toast.success('Account created successfully');
      setIsSuccessful(true);
      router.refresh();
    }
  }, [state, router]);

  const handleSubmit = async (formData: FormData) => {
    const password = formData.get('password') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    setEmail(formData.get('email') as string);
    const result = await register({ status: 'idle' }, formData);
    setState(result);
  };

  const handleGoogleSignIn = async () => {
    try {
      await signIn('google', { callbackUrl: '/' });
    } catch (error) {
      toast.error('Failed to sign in with Google');
    }
  };

  return (
    <div className="container mx-auto px-4 flex-1 flex items-center justify-center">
      <div className="auth-container w-full max-w-[480px]">
        <div className="auth-form bg-white rounded-[20px] p-8 shadow-sm border border-[#e5f4f6]">
          <div className="flex flex-col items-center justify-center gap-2 text-center mb-6">
            <h3 className="text-xl font-semibold">Register</h3>
            <p className="text-sm text-gray-500">
              Create an account with your email and password
            </p>
          </div>
          <AuthForm action={handleSubmit} defaultEmail={email}>
            <div className="mb-3">
              <input
                type="password"
                name="confirmPassword"
                placeholder="Confirm Password"
                required
                className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#0594A2]"
              />
            </div>
            <SubmitButton isSuccessful={isSuccessful}>
              <div>Register</div>
            </SubmitButton>

            <div className="divider text-center text-gray-400 text-sm">
              or continue with
            </div>

            <button
              type="button"
              onClick={handleGoogleSignIn}
              className="google-btn"
            >
              <Image
                src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.1/icons/google.svg"
                alt="Google"
                width="20"
                height="20"
              />
              Continue with Google
            </button>

            <div className="footer-links text-center mt-4 text-sm text-gray-500 flex flex-col gap-2">
              <p>
                {'Already have an account? '}
                <Link
                  href="/login"
                  className="text-[#0594A2] hover:underline font-medium"
                >
                  Login
                </Link>
                {' instead.'}
              </p>
              <Link
                href="/reset-password"
                className="text-[#0594A2] hover:underline font-medium"
              >
                Reset Password
              </Link>
            </div>
          </AuthForm>
        </div>
      </div>
    </div>
  );
}
