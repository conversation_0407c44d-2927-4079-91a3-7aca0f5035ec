import type { NextAuthConfig } from 'next-auth';

export const authConfig = {
  pages: {
    signIn: '/login',
    newUser: '/',
  },
  providers: [
    // added later in auth.ts since it requires bcrypt which is only compatible with Node.js
    // while this file is also used in non-Node.js environments
  ],
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnChat = nextUrl.pathname.startsWith('/');
      const isOnRegister = nextUrl.pathname.startsWith('/register');
      const isOnLogin = nextUrl.pathname.startsWith('/login');
      const isOnResetPassword = nextUrl.pathname.startsWith('/reset-password');
      const isOnNewPassword = nextUrl.pathname.startsWith('/new-password');
      const isOnTalk = nextUrl.pathname.startsWith('/talk');
      if (
        isLoggedIn &&
        (isOnLogin || isOnRegister || isOnResetPassword || isOnNewPassword)
      ) {
        return Response.redirect(new URL('/', nextUrl as unknown as URL));
      }

      if (
        isOnRegister ||
        isOnLogin ||
        isOnResetPassword ||
        isOnNewPassword ||
        isOnChat ||
        isOnTalk
      ) {
        return true; // Allow access to register, login, reset-password, and talk pages
      }

      return true;
    },
  },
} satisfies NextAuthConfig;
