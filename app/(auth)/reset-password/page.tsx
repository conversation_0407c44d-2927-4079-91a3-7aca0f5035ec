'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';

import { resetPassword } from '../actions';

export default function Page() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);

  const handleSubmit = async (formData: FormData) => {
    setEmail(formData.get('email') as string);

    try {
      const result = await resetPassword(formData);

      if (result.status === 'not_found') {
        toast.error('Account not found!');
      } else if (result.status === 'failed') {
        toast.error('Failed to reset password!');
      } else if (result.status === 'invalid_data') {
        toast.error('Failed validating your submission!');
      } else if (result.status === 'success') {
        setIsSuccessful(true);
        toast.success('Password reset email sent!');
        router.push('/login');
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    }
  };

  return (
    <div className="container mx-auto px-4 flex-1 flex items-center justify-center">
      <div className="auth-container w-full max-w-[480px]">
        <div className="auth-form bg-white rounded-[20px] p-8 shadow-sm border border-[#e5f4f6]">
          <div className="flex flex-col items-center justify-center gap-2 text-center mb-6">
            <h3 className="text-xl font-semibold">Reset Password</h3>
            <p className="text-sm text-gray-500">
              Enter your email to reset your password
            </p>
          </div>
          <AuthForm action={handleSubmit} defaultEmail={email} hidePassword>
            <SubmitButton isSuccessful={isSuccessful}>
              <div>Reset Password</div>
            </SubmitButton>
            <div className="footer-links text-center mt-4 text-sm text-gray-500 flex flex-col gap-2">
              <p>
                {'Remember your password? '}
                <Link
                  href="/login"
                  className="text-[#0594A2] hover:underline font-medium"
                >
                  Login
                </Link>
                {' instead.'}
              </p>
            </div>
          </AuthForm>
        </div>
      </div>
    </div>
  );
}
