import { compare } from 'bcryptjs';
import NextAuth from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import { randomBytes } from 'crypto';

import { getUser, updateTemporaryUser, createUser } from '@/lib/db/queries';
import { getTempUserId, deleteTempUserCookie } from '@/app/actions/cookies';

import { authConfig } from './auth.config';

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    Credentials({
      id: 'credentials',
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        const { email, password } = credentials as {
          email: string;
          password: string;
        };

        const [user] = await getUser(email);
        if (!user?.password) {
          console.log('[Auth] No password found for user:', email);
          return null;
        }

        console.log('[Auth] Comparing passwords for:', email);
        const passwordsMatch = await compare(password, user.password);
        console.log(
          '[Auth] Passwords match:',
          passwordsMatch,
          'for user:',
          email,
        );

        if (!passwordsMatch) return null;

        return {
          id: user.id,
          email: user.email,
          name: user.email,
        };
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google') {
        const [existingUser] = await getUser(user.email!);

        // Get temp user ID from cookie if it exists
        const tempUserId = await getTempUserId();

        if (existingUser) {
          // If user exists and is not temporary, use their database ID
          if (!existingUser.is_temporary) {
            user.id = existingUser.id;
          } else {
            // If the existing user is temporary, update it with Google info
            const updatedUser = await updateTemporaryUser({
              id: existingUser.id,
              email: user.email!,
              password: null, // No password needed for Google auth
            });
            user.id = updatedUser[0].id;
          }
        } else if (tempUserId) {
          // If no existing user but we have a temp user, convert it
          const updatedUser = await updateTemporaryUser({
            id: tempUserId,
            email: user.email!,
            password: null, // No password needed for Google auth
          });
          user.id = updatedUser[0].id;

          // Clear the temporary user cookie
          await deleteTempUserCookie();
        } else {
          // Create a new user in the database for Google sign-in
          const securePassword = randomBytes(32).toString('hex');
          const newUser = await createUser(user.email!, securePassword);
          user.id = newUser[0].id;
        }
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
      }
      return session;
    },
  },
  session: { strategy: 'jwt' },
  pages: {
    signIn: '/login',
    error: '/login',
  },
});
