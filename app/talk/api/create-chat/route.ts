import { auth } from '@/app/(auth)/auth';
import { saveChat } from '@/lib/db/queries';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  console.log('🎙️ Create chat API called');

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  console.log('👤 User info:', {
    sessionUser: session?.user?.email || 'none',
    tempUserId: tempUserId || 'none',
  });

  if (!session?.user?.email && !tempUserId) {
    console.error('❌ Unauthorized: No session user or temp user ID');
    return new Response('Unauthorized', { status: 401 });
  }

  const body = await request.json();
  const { chatId, userId, title, isVoiceChat } = body;

  console.log('📝 Chat creation details:', {
    chatId,
    userId,
    title,
    isVoiceChat,
  });

  if (!chatId || !userId || !title) {
    console.error('❌ Missing required fields:', { chatId, userId, title });
    return new Response('Missing required fields', { status: 400 });
  }

  try {
    console.log('💾 Saving chat to database...');

    await saveChat({
      id: chatId,
      userId,
      title,
      isVoiceChat,
    });

    console.log('✅ Chat created successfully');
    return new Response('Chat created', { status: 200 });
  } catch (error) {
    console.error('❌ Failed to create chat:', error);
    return new Response('Failed to create chat', { status: 500 });
  }
}
