import { NextResponse } from 'next/server';
import { getMessagesByChatId, getMessageEmotions } from '@/lib/db/queries';

interface EmotionData {
  id: string;
  messageId: string;
  chatId: string;
  userId: string;
  emotions: Record<string, number>;
  createdAt: Date;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');

    if (!chatId) {
      return NextResponse.json(
        { error: 'Chat ID is required' },
        { status: 400 },
      );
    }

    // Get all messages for the chat
    const messages = await getMessagesByChatId({ id: chatId });

    // Get emotions for all messages
    const emotions = (await getMessageEmotions({ chatId })) as EmotionData[];

    // Calculate average emotions
    const averageEmotions = emotions.reduce(
      (acc: Record<string, number>, curr) => {
        Object.entries(curr.emotions).forEach(([emotion, score]) => {
          acc[emotion] = (acc[emotion] || 0) + score;
        });
        return acc;
      },
      {},
    );

    // Normalize emotions and get top 5
    const topEmotions = Object.entries(averageEmotions)
      .map(([emotion, score]) => ({
        emotion,
        score: score / emotions.length,
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .reduce(
        (acc, { emotion, score }) => {
          acc[emotion] = score;
          return acc;
        },
        {} as Record<string, number>,
      );

    return NextResponse.json({
      messageCount: messages.length,
      emotions: topEmotions,
    });
  } catch (error) {
    console.error('Failed to fetch call summary:', error);
    return NextResponse.json(
      { error: 'Failed to fetch call summary' },
      { status: 500 },
    );
  }
}
