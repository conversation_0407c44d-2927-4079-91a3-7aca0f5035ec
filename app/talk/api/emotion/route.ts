import { auth } from '@/app/(auth)/auth';
import { saveEmotionMessage } from '@/lib/db/queries';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const body = await request.json();
    const { messageId, chatId, emotions } = body;

    if (!messageId || !chatId || !emotions || typeof emotions !== 'object') {
      return new Response('Missing or invalid fields', { status: 400 });
    }

    await saveEmotionMessage({
      messageId,
      chatId,
      userId: session?.user?.id || tempUserId!,
      emotions,
    });

    return new Response('Emotions saved', { status: 200 });
  } catch (error) {
    return new Response('Failed to save emotions', { status: 500 });
  }
}
