import { getHumeAccessToken } from '@/lib/hume';
import { auth } from '@/app/(auth)/auth';
import ChatWrapper from '@/components/talk/ChatWrapper';
import Link from 'next/link';
import { createAndSetTempUserIfMissing } from '../actions/cookies';

export default async function Page() {
  const session = await auth();

  const tempUserId = await createAndSetTempUserIfMissing();

  const accessToken = await getHumeAccessToken();

  if (!accessToken) {
    console.error('Failed to obtain Hume access token');
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="w-full mx-auto px-4 py-6 gradient-primary text-white">
        <div className="flex items-center justify-between border-b">
          <Link href="/" className="p-2">
            ←
          </Link>
          <Link href="/" className="block p-4 text-2xl text-center">
            GentleGossip
          </Link>
          <div className="w-6"></div>
        </div>
      </header>
      <main className="flex-1 relative">
        {accessToken && (
          <ChatWrapper
            accessToken={accessToken}
            userId={session?.user?.id || tempUserId!}
          />
        )}
      </main>
    </div>
  );
}
