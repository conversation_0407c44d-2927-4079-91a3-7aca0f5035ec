import { notFound } from 'next/navigation';
import { getBlogPost } from '@/lib/blog';
import Link from 'next/link';

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function BlogPostPage({ params }: PageProps) {
  const resolvedParams = await params;
  const post = await getBlogPost(resolvedParams.slug);

  if (!post) {
    notFound();
  }

  return (
    <>
      <header className="w-full mx-auto px-4 py-6 gradient-primary text-white">
        <div className="flex items-center justify-between border-b text-white">
          <Link href="/blog" className="p-2">
            ←
          </Link>
          <Link href="/" className="block p-4 text-2xl text-center">
            GentleGossip
          </Link>
          <div className="w-6"></div>
        </div>
      </header>
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-prose mx-auto">
          <h1 className="text-3xl font-bold mb-4">{post.title}</h1>
          <div
            className="prose prose-lg dark:prose-invert"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </div>
      </div>
    </>
  );
}
