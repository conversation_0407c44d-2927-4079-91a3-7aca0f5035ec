import Link from 'next/link';
import { getBlogPosts } from '@/lib/blog';

export default async function BlogIndexPage() {
  const posts = await getBlogPosts();

  return (
    <>
      <header className="w-full mx-auto px-4 py-6 gradient-primary text-white">
        <div className="flex items-center justify-between border-b">
          <Link href="/" className="p-2 text-white">
            ←
          </Link>
          <Link href="/" className="block p-4 text-2xl text-center text-white">
            GentleGossip
          </Link>
          <div className="w-6"></div>
        </div>
      </header>
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-3xl font-bold mb-8">Blog Posts</h1>
        <ul className="space-y-4">
          {posts.map((post) => (
            <li key={post.slug}>
              <Link
                href={`/blog/${post.slug}`}
                className="text-xl hover:underline"
              >
                {post.title}
              </Link>
              <p className="text-gray-500">{post.excerpt}</p>
            </li>
          ))}
        </ul>
      </div>
    </>
  );
}
