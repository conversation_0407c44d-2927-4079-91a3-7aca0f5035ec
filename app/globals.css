@import '../styles/tiptap.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  --color-primary: #0594A2;
  --color-secondary: #4AC1C7;
  --color-tertiary: #97E5EB;
  --color-light: #D4F1F4;
  --gradient-primary: linear-gradient(135deg, #0594A2 0%, #4AC1C7 100%);
  --gradient-secondary: linear-gradient(135deg, #97E5EB 0%, #D4F1F4 100%);
  --gradient-background: linear-gradient(170deg, #fff 0%, #D4F1F4 100%);
  --gradient-chat-bubble: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.95) 100%);
  --gradient-user-bubble: linear-gradient(135deg, #0594A2 0%, #4AC1C7 100%);
  --gradient-danger: linear-gradient(135deg, #FF6B6B 0%, #FF4949 100%);
  --text-primary: #264653;
  --text-secondary: #ffffff;
  --sidebar-width: 300px;
  --sidebar-background: 0 0% 100%;
  --sidebar-foreground: 197 31% 24%;
  --sidebar-primary: #0594A2;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #4AC1C7;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #D4F1F4;
  --sidebar-ring: #0594A2;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;

  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .welcome-header {
    @apply w-full px-5 py-4 text-center mb-5 flex flex-col items-center gap-4 relative;
    background: linear-gradient(180deg, rgba(255,255,255,0) 0%, var(--color-light) 100%);
  }

  .nav-card {
    @apply bg-white rounded-[20px] p-8 mb-8 no-underline transition-all duration-300 shadow-md flex flex-col items-center text-center;
    color: var(--text-primary);
  }

  .nav-card:hover {
    @apply transform -translate-y-1 shadow-lg;
  }

  .auth-form {
    @apply bg-white rounded-[20px] p-8 shadow-md;
  }

  .form-group {
    @apply mb-6;
  }

  .auth-btn {
    @apply w-full py-3 px-3 rounded text-sm cursor-pointer transition-opacity duration-300;
    background: var(--gradient-primary);
    color: var(--text-secondary);
  }

  .google-btn {
    @apply w-full py-3 px-3 bg-white border border-gray-200 rounded text-sm cursor-pointer flex items-center justify-center gap-2 mt-3;
    color: var(--text-primary);
  }

  .gradient-primary {
    background: var(--gradient-primary);
  }

  .chat-header {
    @apply flex sticky bg-white top-0 py-1.5 items-center px-2 md:px-2 gap-1 md:gap-2 border-b border-cyan-100;
  }

  .chat-header-title {
    @apply text-2xl text-cyan-600 font-medium cursor-pointer hover:opacity-80 transition-opacity;
  }

  .chat-header-button {
    @apply order-2 md:order-1 md:px-2 px-2 md:h-fit text-white border-none rounded text-sm;
    background: var(--gradient-primary);
    min-width: auto;
  }

  /* Mobile-specific optimizations */
  @media (max-width: 767px) {
    .chat-header {
      @apply py-2 gap-1;
    }

    .chat-header-button {
      @apply px-2 py-2 min-w-0;
      font-size: 0.75rem;
    }

    .chat-header-title {
      @apply text-lg;
    }

    /* Reduce overall spacing on mobile */
    body {
      font-size: 14px;
    }

    /* Make tooltips smaller on mobile */
    [data-radix-tooltip-content] {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    /* Optimize button spacing in mobile */
    .flex.gap-2 {
      gap: 0.25rem;
    }
  }
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

@layer base {
  * {
    @apply border-border;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, sans-serif;
  }

  body {
    @apply bg-background text-foreground;
    background: var(--gradient-background);
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  @font-face {
    font-family: "geist";
    font-style: normal;
    font-weight: 100 900;
    src: url(/fonts/geist.woff2) format("woff2");
  }

  @font-face {
    font-family: "geist-mono";
    font-style: normal;
    font-weight: 100 900;
    src: url(/fonts/geist-mono.woff2) format("woff2");
  }
}

.skeleton {
  * {
    pointer-events: none !important;
  }

  *[class^="text-"] {
    color: transparent;
    @apply rounded-md bg-foreground/20 select-none animate-pulse;
  }

  .skeleton-bg {
    @apply bg-foreground/10;
  }

  .skeleton-div {
    @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.suggestion-highlight {
  @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}
