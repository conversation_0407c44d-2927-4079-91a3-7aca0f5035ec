import { JournalFeed } from '@/components/journal-feed';
import { auth } from '../../(auth)/auth';
import { getDocumentsByUserId } from '@/lib/db/queries';
import { getTempUserId } from '@/app/actions/cookies';

export default async function Page() {
  const session = await auth();
  const tempUserId = await getTempUserId();

  // Fetch all journal entries for the user if they're authenticated
  let allDocuments: any[] = [];
  const userId = session?.user?.id || tempUserId;

  if (userId) {
    try {
      allDocuments = await getDocumentsByUserId({ id: userId });
    } catch (error) {
      console.error('Failed to fetch journal entries:', error);
      // Continue with empty documents if fetch fails
    }
  }

  return (
    <div className="flex flex-col h-full">
      <JournalFeed
        initialDocuments={allDocuments}
        isUserTemporary={!session?.user?.id}
      />
    </div>
  );
}
