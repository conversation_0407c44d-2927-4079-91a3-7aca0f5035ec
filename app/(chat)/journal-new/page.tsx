import { redirect } from 'next/navigation';
import { auth } from '@/app/(auth)/auth';
import { getTempUserId } from '@/app/actions/cookies';
import { generateUUID } from '@/lib/utils';

export default async function JournalNewPage() {
  const session = await auth();
  const tempUserId = await getTempUserId();

  // Check if user is authenticated (either logged in or has temp user)
  if (!session?.user && !tempUserId) {
    redirect('/login');
  }

  // Generate a new UUID for the journal entry
  const newJournalId = generateUUID();

  // Redirect to the journal editor with the new ID
  redirect(`/journal/${newJournalId}`);
}
