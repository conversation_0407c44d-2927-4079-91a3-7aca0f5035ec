import { notFound } from 'next/navigation';

import { auth } from '@/app/(auth)/auth';
import { Chat } from '@/components/chat';
import { DEFAULT_MODEL_NAME, models } from '@/lib/ai/models';
import { getChatById, getMessagesByChatId } from '@/lib/db/queries';
import { convertToUIMessages } from '@/lib/utils';
import { getTempUserId, getModelId } from '@/app/actions/cookies';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;
  const chat = await getChatById({ id });

  const session = await auth();
  const tempUserId = await getTempUserId();
  const userId = session?.user?.id || tempUserId;

  // If chat doesn't exist, check if this is a valid scenario for temporary users
  if (!chat) {
    // If no user is authenticated at all, show 404
    if (!userId) {
      return notFound();
    }

    // For temporary users, allow creating a new chat with this ID
    // For authenticated users, only allow if they have a valid session
    if (!session?.user?.id && !tempUserId) {
      return notFound();
    }

    // Create a new chat session with empty messages
    const modelIdFromCookie = await getModelId();
    const selectedModelId =
      models.find((model) => model.id === modelIdFromCookie)?.id ||
      DEFAULT_MODEL_NAME;

    return (
      <Chat
        id={id}
        initialMessages={[]}
        selectedModelId={selectedModelId}
        selectedVisibilityType="private"
        isReadonly={false}
        isUserTemporary={!session?.user?.id}
      />
    );
  }

  // Chat exists, proceed with normal flow
  if (chat.visibility === 'private') {
    if (!session?.user && !tempUserId) {
      return notFound();
    }

    if (session?.user?.id !== chat.userId && tempUserId !== chat.userId) {
      return notFound();
    }
  }

  const messagesFromDb = await getMessagesByChatId({
    id,
    userId,
    limit: 50,
    offset: 0,
    orderBy: 'desc',
  });

  const modelIdFromCookie = await getModelId();
  const selectedModelId =
    models.find((model) => model.id === modelIdFromCookie)?.id ||
    DEFAULT_MODEL_NAME;

  return (
    <Chat
      id={chat.id}
      initialMessages={convertToUIMessages([...messagesFromDb].reverse())}
      selectedModelId={selectedModelId}
      selectedVisibilityType={chat.visibility}
      isReadonly={
        chat.isVoiceChat ||
        (session?.user?.id !== chat.userId && tempUserId !== chat.userId)
      }
      isUserTemporary={!session?.user?.id}
    />
  );
}
