import { Chat } from '@/components/chat';
import { DEFAULT_MODEL_NAME, } from '@/lib/ai/models';
import { generateUUID, convertToUIMessages } from '@/lib/utils';
import { auth } from '../(auth)/auth';
import { getAllMessagesByUserId } from '@/lib/db/queries';
import { TempUserChatRedirect } from '@/components/temp-user-chat-redirect';
import { getTempUserId } from '@/app/actions/cookies';

export default async function Page() {
  const id = generateUUID();
  const selectedModelId = DEFAULT_MODEL_NAME;
  const session = await auth();
  const tempUserId = await getTempUserId();

  // Fetch all messages for the user if they're authenticated or temporary
  let allMessages: any[] = [];
  const userId = session?.user?.id || tempUserId;

  if (userId) {
    try {
      const messagesFromDb = await getAllMessagesByUserId(userId, 25, 0);
      allMessages = convertToUIMessages([...messagesFromDb].reverse());
    } catch (error) {
      console.error('Failed to fetch all messages:', error);
      // Continue with empty messages if fetch fails
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Handle redirect for temporary users with existing chats */}
      {!session?.user?.id && <TempUserChatRedirect />}

      <Chat
        key={id}
        id={id}
        initialMessages={allMessages}
        selectedModelId={selectedModelId}
        selectedVisibilityType="private"
        isReadonly={false}
        isUserTemporary={!session?.user?.id}
        isAllMessagesView={true}
      />
    </div>
  );
}
