import { auth } from '@/app/(auth)/auth';
import { getDocumentsByUserId } from '@/lib/db/queries';
import { cookies } from 'next/headers';

export async function GET() {
  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  const userId = session?.user?.id || tempUserId;
  
  if (!userId) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  const documents = await getDocumentsByUserId({ id: userId });
  return Response.json(documents);
}
