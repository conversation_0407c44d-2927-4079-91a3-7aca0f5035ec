import { auth } from '@/app/(auth)/auth';
import { clearRecollection, unclearRecollection } from '@/lib/db/queries';

export async function POST(request: Request) {
  const session = await auth();

  if (!session || !session.user) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  try {
    const { recollectionId, action } = await request.json();

    if (!recollectionId) {
      return Response.json({ error: 'Recollection ID is required' }, { status: 400 });
    }

    if (action === 'clear') {
      await clearRecollection({
        userId: session.user.id!,
        recollectionId,
      });
      return Response.json({ success: true, cleared: true });
    } else if (action === 'unclear') {
      await unclearRecollection({
        userId: session.user.id!,
        recollectionId,
      });
      return Response.json({ success: true, cleared: false });
    } else {
      return Response.json({ error: 'Invalid action. Use "clear" or "unclear"' }, { status: 400 });
    }
  } catch (error) {
    console.error('Failed to process recollection clear action:', error);
    return Response.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 