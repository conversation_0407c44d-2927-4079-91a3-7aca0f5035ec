import { auth } from '@/app/(auth)/auth';
import { getDailyRecollectionsByUserId } from '@/lib/db/queries';

export async function GET() {
  const session = await auth();

  if (!session || !session.user) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  try {
    const recollections = await getDailyRecollectionsByUserId(session.user.id!);
    return Response.json(recollections);
  } catch (error) {
    console.error('Failed to fetch daily recollections:', error);
    return Response.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
