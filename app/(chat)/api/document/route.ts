import { auth } from '@/app/(auth)/auth';
import {
  deleteDocumentsByIdAfterTimestamp,
  getDocumentsById,
  saveDocument,
  softDeleteDocumentById,
} from '@/lib/db/queries';
import { cookies } from 'next/headers';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Missing id', { status: 400 });
  }

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  const documents = await getDocumentsById({ id });

  const [document] = documents;

  if (!document) {
    return new Response('Not Found', { status: 404 });
  }

  if (document.userId !== session?.user?.id && document.userId !== tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  return Response.json(documents, { status: 200 });
}

export async function POST(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Missing id', { status: 400 });
  }

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { content, title }: { content: string; title: string } =
    await request.json();

  if (session?.user?.id || tempUserId) {
    const document = await saveDocument({
      id,
      content,
      title,
      userId: session?.user?.id || tempUserId || '',
    });

    return Response.json(document, { status: 200 });
  }
  return new Response('Unauthorized', { status: 401 });
}

export async function PATCH(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  const { timestamp }: { timestamp: string } = await request.json();

  if (!id) {
    return new Response('Missing id', { status: 400 });
  }

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  const documents = await getDocumentsById({ id });

  const [document] = documents;

  if (document.userId !== session?.user?.id && document.userId !== tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  await deleteDocumentsByIdAfterTimestamp({
    id,
    timestamp: new Date(timestamp),
  });

  return new Response('Deleted', { status: 200 });
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Missing id', { status: 400 });
  }

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  const documents = await getDocumentsById({ id });
  const [document] = documents;

  if (!document) {
    return new Response('Not Found', { status: 404 });
  }

  if (document.userId !== session?.user?.id && document.userId !== tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Soft delete the document
  await softDeleteDocumentById({ id });

  return new Response('Document deleted', { status: 200 });
}
