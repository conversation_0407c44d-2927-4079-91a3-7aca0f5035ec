import { auth } from '@/app/(auth)/auth';
import { getVotesByChatId, getVotesByMessageIds, voteMessage } from '@/lib/db/queries';
import { cookies } from 'next/headers';
import { isValidUUID, generateUUID } from '@/lib/utils';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');
  const messageIdsParam = searchParams.get('messageIds');

  // Support both chatId and messageIds parameters
  if (!chatId && !messageIdsParam) {
    return new Response('Either chatId or messageIds is required', { status: 400 });
  }

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    let votes;

    if (messageIdsParam) {
      // Parse messageIds from comma-separated string
      const messageIds = messageIdsParam.split(',').filter(id => isValidUUID(id));
      if (messageIds.length === 0) {
        return new Response('No valid messageIds provided', { status: 400 });
      }
      votes = await getVotesByMessageIds({ messageIds });
    } else if (chatId) {
      // Validate that chatId is a valid UUID
      if (!isValidUUID(chatId)) {
        console.error('Invalid chatId format:', chatId);
        return new Response('Invalid chatId format', { status: 400 });
      }
      votes = await getVotesByChatId({ id: chatId });
    }

    return Response.json(votes, { status: 200 });
  } catch (error) {
    console.error('Failed to get votes:', error);
    return new Response('Failed to get votes', { status: 500 });
  }
}

export async function PATCH(request: Request) {
  const {
    chatId,
    messageId,
    type,
  }: { chatId?: string; messageId: string; type: 'up' | 'down' } =
    await request.json();

  if (!messageId || !type) {
    return new Response('messageId and type are required', { status: 400 });
  }

  if (!isValidUUID(messageId)) {
    console.error('Invalid messageId format:', messageId);
    return new Response('Invalid messageId format', { status: 400 });
  }

  // Generate chatId if not provided
  const finalChatId = chatId && isValidUUID(chatId) ? chatId : generateUUID();

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  const userId = session?.user?.id || tempUserId!;

  try {
    await voteMessage({
      chatId: finalChatId,
      messageId,
      type: type,
      userId,
    });

    return new Response('Message voted', { status: 200 });
  } catch (error) {
    console.error('Failed to vote message:', error);
    return new Response('Failed to vote message', { status: 500 });
  }
}
