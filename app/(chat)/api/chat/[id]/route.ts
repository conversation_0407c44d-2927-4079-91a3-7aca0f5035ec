import { auth } from '@/app/(auth)/auth';
import {
  deleteChatById,
  getChatById,
  getMessagesByChatId,
} from '@/lib/db/queries';
import { convertToUIMessages } from '@/lib/utils';
import { getTempUserId } from '@/app/actions/cookies';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;
  const { searchParams } = new URL(request.url);
  const limit = Number.parseInt(searchParams.get('limit') || '500', 10);
  const offset = Number.parseInt(searchParams.get('offset') || '0', 10);
  const messagesOnly = searchParams.get('messagesOnly') === 'true';

  const session = await auth();
  const tempUserId = await getTempUserId();

  // Check if user is authenticated or has a temporary user ID
  const userId = session?.user?.id || tempUserId;
  if (!userId) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  // Get chat
  const chat = await getChatById({ id });

  if (!chat) {
    return new Response('Chat not found', {
      status: 404,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // Check permissions
  if (chat.visibility === 'private') {
    if (session?.user?.id !== chat.userId && tempUserId !== chat.userId) {
      return new Response('Forbidden', {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }
  }

  // Get messages with pagination
  const messagesFromDb = await getMessagesByChatId({
    id,
    userId,
    limit,
    offset,
    orderBy: 'desc',
  });

  const uiMessages = convertToUIMessages([...messagesFromDb].reverse());

  // If messagesOnly flag is set, return just the messages
  if (messagesOnly) {
    return new Response(JSON.stringify(uiMessages), {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // Otherwise return full chat data
  return new Response(
    JSON.stringify({
      messages: uiMessages,
      chat: {
        id: chat.id,
        visibility: chat.visibility,
        isReadonly: userId !== chat.userId,
        isUserTemporary: !session?.user?.id,
      },
    }),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;

  const session = await auth();

  if (!session || !session.user) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  const userId = session.user.id;

  // Get chat
  const chat = await getChatById({ id });

  if (!chat) {
    return new Response('Chat not found', {
      status: 404,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // Check if user owns the chat
  if (userId !== chat.userId) {
    return new Response('Forbidden', {
      status: 403,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // Delete chat
  await deleteChatById({
    id,
  });

  return new Response(null, { status: 204 });
}
