import { getAllMessagesByUserId } from '@/lib/db/queries';
import { auth } from '@/app/(auth)/auth';
import { convertToUIMessages } from '@/lib/utils';
import { getTempUserId } from '@/app/actions/cookies';

export async function GET(request: Request) {
  const session = await auth();
  const tempUserId = await getTempUserId();

  // Check if user is authenticated or has a temporary user ID
  const userId = session?.user?.id || tempUserId;
  if (!userId) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const limit = Number.parseInt(searchParams.get('limit') || '200', 10);
  const offset = Number.parseInt(searchParams.get('offset') || '0', 10);

  try {
    const messagesFromDb = await getAllMessagesByUserId(userId, limit, offset);
    const uiMessages = convertToUIMessages([...messagesFromDb].reverse());
    return Response.json(uiMessages);
  } catch (error) {
    console.error('Error fetching paginated messages:', error);
    return Response.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
