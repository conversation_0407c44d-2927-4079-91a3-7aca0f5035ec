import { auth } from '@/app/(auth)/auth';
import { saveClearedMessages, getAllMessagesByUserId } from '@/lib/db/queries';
import { getTempUserId } from '@/app/actions/cookies';
import { NextRequest, NextResponse } from 'next/server';
import { generateUUID } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    const tempUserId = await getTempUserId();

    // Get user ID from either session or temporary user
    const userId = session?.user?.id || tempUserId;
    const isTemporaryUser = !session?.user?.id && !!tempUserId;

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user's most recent message to use as the last cleared message
    const recentMessages = await getAllMessagesByUserId(userId, 1, 0);

    if (recentMessages.length === 0) {
      // If there are no messages, consider it already cleared
      // For temporary users, still generate a new chat ID
      if (isTemporaryUser) {
        const newChatId = generateUUID();
        return NextResponse.json({
          success: true,
          message: 'No messages to clear. Chat is already empty.',
          newChatId: newChatId,
          shouldRedirect: true,
        });
      }

      return NextResponse.json({
        success: true,
        message: 'No messages to clear. Chat is already empty.',
      });
    }

    const lastMessageId = recentMessages[0].id;

    // Save the cleared messages record
    await saveClearedMessages({
      userId: userId,
      lastClearedMessageId: lastMessageId,
    });

    // For temporary users, generate a new chat ID and indicate they should be redirected
    if (isTemporaryUser) {
      const newChatId = generateUUID();
      return NextResponse.json({
        success: true,
        message: 'Messages cleared from UI successfully. Note: Messages are still stored in AI memory for context.',
        newChatId: newChatId,
        shouldRedirect: true,
      });
    }

    // For authenticated users, keep the existing behavior
    return NextResponse.json({
      success: true,
      message: 'Messages cleared from UI successfully. Note: Messages are still stored in AI memory for context.',
    });
  } catch (error) {
    console.error('Error clearing messages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
