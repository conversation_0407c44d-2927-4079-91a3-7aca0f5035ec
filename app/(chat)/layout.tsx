import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { SessionProvider } from 'next-auth/react';
import { AppSidebar } from '@/components/app-sidebar';
import { RecollectionDialogProvider } from '@/components/recollection-dialog-context';
import { RecollectionDialog } from '@/components/recollection-dialog';

import { auth } from '../(auth)/auth';
import { getSidebarState } from '../actions/cookies';

export const experimental_ppr = true;

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [session, sidebarState] = await Promise.all([
    auth(),
    getSidebarState(),
  ]);
  const isCollapsed = sidebarState !== 'true';

  return (
    <SessionProvider session={session}>
      <RecollectionDialogProvider>
        <SidebarProvider defaultOpen={!isCollapsed}>
          <AppSidebar user={session?.user} />
          <SidebarInset>{children}</SidebarInset>
        </SidebarProvider>
        <RecollectionDialog />
      </RecollectionDialogProvider>
    </SessionProvider>
  );
}
