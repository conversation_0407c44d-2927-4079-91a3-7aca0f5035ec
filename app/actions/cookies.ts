'use server';

import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function createAndSetTempUserIfMissing() {
  console.log('Executing createAndSetTempUserIfMissing');
  const cookieStore = cookies(); // Use await for reading
  const tempUserId = (await cookieStore).get('tempUserId')?.value; // Read tempUserId from the store
  console.log('Initial tempUserId:', tempUserId);

  console.log('tempUserId found, returning existing one.');
  return tempUserId;
}

export async function getTempUserId() {
  const cookieStore = await cookies();
  return cookieStore.get('tempUserId')?.value;
}

export async function getModelId() {
  const cookieStore = await cookies();
  return cookieStore.get('model-id')?.value;
}

export async function setModelId(modelId: string) {
  const response = NextResponse.next();
  response.cookies.set('model-id', modelId);
  return response;
}

export async function getSidebarState() {
  const cookieStore = await cookies();
  return cookieStore.get('sidebar:state')?.value;
}

export async function setSidebarState(state: string) {
  const response = NextResponse.next();
  response.cookies.set('sidebar:state', state);
  return response;
}

export async function deleteTempUserCookie() {
  const response = NextResponse.next();
  response.cookies.delete('tempUserId');
  return response;
}
