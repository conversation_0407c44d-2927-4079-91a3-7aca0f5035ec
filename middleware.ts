import { NextResponse } from 'next/server';
import NextAuth from 'next-auth';
import { authConfig } from '@/app/(auth)/auth.config';
import { RateLimiterService } from '@/lib/rate-limiter';

// NextAuth middleware
const nextAuthMiddleware = NextAuth(authConfig).auth;

// Export NextAuth middleware as default
export default nextAuthMiddleware;

// Combine rate limiting and NextAuth middleware
export async function middleware(request: any) {
  const path = request.nextUrl.pathname;

  // Apply cookies to /talk path.
  const cookiePaths = ['/talk'];
  if (cookiePaths.some((paths) => path.startsWith(paths))) {
    const tempUserIdCookie = request.cookies.get('tempUserId');

    if (!tempUserIdCookie) {
      return NextResponse.rewrite(new URL('/api/auth/temp', request.url));
    }
  }

  // Only apply rate limiting to specified routes
  const protectedPaths = [
    '/login',
    '/register',
    '/reset-password',
    '/chat',
    '/api',
  ];
  if (protectedPaths.some((protectedPath) => path.startsWith(protectedPath))) {
    // Get client identifier
    const forwardedFor = request.headers.get('x-forwarded-for');
    const clientIp = forwardedFor
      ? forwardedFor.split(',')[0].trim()
      : 'unknown';

    // Check rate limit
    const { allowed, remaining, resetTime } =
      await RateLimiterService.checkRateLimit(clientIp);

    if (!allowed) {
      const timeRemaining = Math.ceil((resetTime - Date.now()) / 1000);

      return NextResponse.json(
        { error: 'Too many requests, please try again later.' },
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': String(timeRemaining),
            'X-RateLimit-Limit': '100',
            'X-RateLimit-Remaining': String(remaining),
            'X-RateLimit-Reset': String(resetTime),
          },
        },
      );
    }
  }

  // Apply NextAuth middleware after rate limiting
  return nextAuthMiddleware(request);
}

export const config = {
  matcher: [
    '/',
    '/:id',
    '/api/:path*',
    '/login',
    '/register',
    '/reset-password',
    '/chat/:id',
    '/api/chat',
    '/api/history',
    '/api/vote/:id',
  ],
};
