'use client';

import { useState } from 'react';
import useS<PERSON> from 'swr';
import type { User } from 'next-auth';
import { format } from 'date-fns';

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { CrossIcon } from '@/components/icons';
import type { Recollection } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';
import { useRecollectionDialog } from './recollection-dialog-context';

interface RecollectionItemProps {
  recollection: Recollection;
  onSelect: (recollection: Recollection) => void;
  onClear: (recollectionId: string) => void;
}

function RecollectionItem({ recollection, onSelect, onClear }: RecollectionItemProps) {
  const formatPeriod = (start: Date, end: Date, granularity: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    
    switch (granularity) {
      case 'hourly':
        return `${format(startDate, 'MMM d, h:mm a')} - ${format(endDate, 'h:mm a')}`;
      case 'daily':
        return format(startDate, 'MMM d, yyyy');
      case 'weekly':
        return `${format(startDate, 'MMM d')} - ${format(endDate, 'MMM d, yyyy')}`;
      case 'monthly':
        return format(startDate, 'MMMM yyyy');
      default:
        return format(startDate, 'MMM d, yyyy');
    }
  };

  const getGranularityConfig = (granularity: string) => {
    switch (granularity) {
      case 'hourly':
        return { icon: '🕐', color: 'text-slate-600', bgColor: 'bg-slate-50 hover:bg-slate-100', label: 'Hourly' };
      case 'daily':
        return { icon: '📅', color: 'text-slate-700', bgColor: 'bg-teal-50/50 hover:bg-teal-50', label: 'Daily' };
      case 'weekly':
        return { icon: '📊', color: 'text-slate-700', bgColor: 'bg-teal-100/30 hover:bg-teal-100/50', label: 'Weekly' };
      case 'monthly':
        return { icon: '📈', color: 'text-slate-700', bgColor: 'bg-teal-100/50 hover:bg-teal-100/70', label: 'Monthly' };
      default:
        return { icon: '📝', color: 'text-slate-600', bgColor: 'bg-slate-50 hover:bg-slate-100', label: 'Summary' };
    }
  };

  const truncateSummary = (text: string, maxLength: number = 80) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  const config = getGranularityConfig(recollection.granularity);

  const handleClear = () => {
    onClear(recollection.recollection_id);
  };

  return (
    <SidebarMenuItem>
      <div className="relative group">
        <SidebarMenuButton
          onClick={() => {
            onSelect(recollection);
          }}
          className={`flex flex-col items-start gap-2 p-4 h-auto w-full border border-gray-200/50 rounded-lg transition-all duration-200 ${config.bgColor} hover:shadow-sm`}
        >
          {/* Header with icon and type */}
          <div className="flex items-center gap-2">
            <span className="text-lg">{config.icon}</span>
            <span className={`text-xs font-medium uppercase tracking-wide ${config.color}`}>
              {config.label}
            </span>
          </div>

          {/* Time period */}
          <div className="text-xs text-gray-600 font-medium w-full text-left">
            {formatPeriod(recollection.period_start, recollection.period_end, recollection.granularity)}
          </div>

          {/* Summary preview */}
          <div className="text-xs text-gray-500 leading-relaxed w-full text-left">
            {truncateSummary(recollection.summary)}
          </div>
        </SidebarMenuButton>
        
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 size-6 p-0 opacity-70 hover:opacity-100 transition-all hover:bg-red-100 hover:text-red-600"
              title="Clear recollection"
              onClick={(e) => e.stopPropagation()}
            >
              <CrossIcon size={12} />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent className="bg-white">
            <AlertDialogHeader>
              <AlertDialogTitle>Clear Recollection</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to clear this {recollection.granularity} recollection? This action cannot be undone and the recollection will no longer appear in your sidebar.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleClear}
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              >
                Clear
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </SidebarMenuItem>
  );
}

export function SidebarRecollections({ user }: { user: User | undefined }) {
  const { openDialog } = useRecollectionDialog();
  const [searchQuery, setSearchQuery] = useState('');

  const { data: hourlyRecollections, isLoading: isLoadingHourly, mutate: mutateHourly } = useSWR<Recollection[]>(
    user ? '/api/recollections/hourly' : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  const { data: dailyRecollections, isLoading: isLoadingDaily, mutate: mutateDaily } = useSWR<Recollection[]>(
    user ? '/api/recollections/daily' : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  const { data: weeklyRecollections, isLoading: isLoadingWeekly, mutate: mutateWeekly } = useSWR<Recollection[]>(
    user ? '/api/recollections/weekly' : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  const { data: monthlyRecollections, isLoading: isLoadingMonthly, mutate: mutateMonthly } = useSWR<Recollection[]>(
    user ? '/api/recollections/monthly' : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  const handleRecollectionSelect = (recollection: Recollection) => {
    openDialog(recollection);
  };

  const handleClearRecollection = async (recollectionId: string) => {
    try {
      const response = await fetch('/api/recollections/clear', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recollectionId,
          action: 'clear',
        }),
      });

      if (response.ok) {
        // Refresh all recollection lists to remove the cleared item
        mutateHourly();
        mutateDaily();
        mutateWeekly();
        mutateMonthly();
      } else {
        console.error('Failed to clear recollection');
      }
    } catch (error) {
      console.error('Error clearing recollection:', error);
    }
  };

  // Filter recollections based on search query
  const filterRecollections = (recollections: Recollection[] | undefined) => {
    if (!recollections || !searchQuery.trim()) return recollections || [];
    return recollections.filter(recollection =>
      recollection.summary.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const filteredHourly = filterRecollections(hourlyRecollections);
  const filteredDaily = filterRecollections(dailyRecollections);
  const filteredWeekly = filterRecollections(weeklyRecollections);
  const filteredMonthly = filterRecollections(monthlyRecollections);

  const isLoading = isLoadingHourly || isLoadingDaily || isLoadingWeekly || isLoadingMonthly;

  if (isLoading) {
    return (
      <SidebarGroup>
        <SidebarGroupLabel>Recollections</SidebarGroupLabel>
        <SidebarGroupContent>
          <div className="flex flex-col gap-3 p-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gradient-to-r from-gray-100 to-gray-50 rounded-lg animate-pulse" />
            ))}
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  const hasRecollections = 
    filteredHourly.length > 0 ||
    filteredDaily.length > 0 ||
    filteredWeekly.length > 0 ||
    filteredMonthly.length > 0;

  const totalRecollections = 
    (hourlyRecollections?.length || 0) +
    (dailyRecollections?.length || 0) +
    (weeklyRecollections?.length || 0) +
    (monthlyRecollections?.length || 0);

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="flex items-center justify-between">
        <span>Recollections</span>
        {totalRecollections > 0 && (
          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
            {totalRecollections}
          </span>
        )}
      </SidebarGroupLabel>
      <SidebarGroupContent>
        {totalRecollections > 0 && (
          <div className="px-2 pb-3">
            <Input
              placeholder="Search recollections..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-8 text-xs"
            />
          </div>
        )}

        {!hasRecollections && searchQuery && (
          <div className="text-xs text-muted-foreground p-4 text-center">
            No recollections found matching &quot;{searchQuery}&quot;.
          </div>
        )}

        {!hasRecollections && !searchQuery && totalRecollections === 0 && (
          <div className="text-xs text-muted-foreground p-4 text-center leading-relaxed">
            <div className="mb-2">🌱</div>
            <div>No recollections available yet.</div>
            <div className="mt-1 text-gray-400">Continue chatting to generate conversation summaries.</div>
          </div>
        )}

        {hasRecollections && (
          <SidebarMenu className="gap-3">
                         {filteredHourly.length > 0 && (
               <div>
                 <div className="p-2 text-xs font-medium text-gray-600 flex items-center gap-2">
                   <div className="size-2 bg-slate-300 rounded-full"></div>
                   Recent Hours
                 </div>
                {filteredHourly.map((recollection) => (
                  <RecollectionItem
                    key={recollection.recollection_id}
                    recollection={recollection}
                    onSelect={handleRecollectionSelect}
                    onClear={handleClearRecollection}
                  />
                ))}
              </div>
            )}

                         {filteredDaily.length > 0 && (
               <div>
                 <div className="p-2 text-xs font-medium text-gray-600 flex items-center gap-2">
                   <div className="size-2 bg-teal-300/60 rounded-full"></div>
                   Recent Days
                 </div>
                {filteredDaily.map((recollection) => (
                  <RecollectionItem
                    key={recollection.recollection_id}
                    recollection={recollection}
                    onSelect={handleRecollectionSelect}
                    onClear={handleClearRecollection}
                  />
                ))}
              </div>
            )}

                         {filteredWeekly.length > 0 && (
               <div>
                 <div className="p-2 text-xs font-medium text-gray-600 flex items-center gap-2">
                   <div className="size-2 bg-teal-400/50 rounded-full"></div>
                   Recent Weeks
                 </div>
                {filteredWeekly.map((recollection) => (
                  <RecollectionItem
                    key={recollection.recollection_id}
                    recollection={recollection}
                    onSelect={handleRecollectionSelect}
                    onClear={handleClearRecollection}
                  />
                ))}
              </div>
            )}

                         {filteredMonthly.length > 0 && (
               <div>
                 <div className="p-2 text-xs font-medium text-gray-600 flex items-center gap-2">
                   <div className="size-2 bg-teal-400/70 rounded-full"></div>
                   Older
                 </div>
                {filteredMonthly.map((recollection) => (
                  <RecollectionItem
                    key={recollection.recollection_id}
                    recollection={recollection}
                    onSelect={handleRecollectionSelect}
                    onClear={handleClearRecollection}
                  />
                ))}
              </div>
            )}
          </SidebarMenu>
        )}
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
