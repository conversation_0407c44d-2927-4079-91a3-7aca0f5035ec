'use client';

import { useState } from 'react';
import { Button } from './ui/button';
import { InfoIcon } from './icons';
import { cn } from '@/lib/utils';

interface RetryTestingPanelProps {
  className?: string;
}

export function RetryTestingPanel({ className }: RetryTestingPanelProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const testingMethods = [
    {
      title: 'Standard Testing',
      description: 'Fail first 2 attempts, then succeed',
      url: '?testRetry=true',
      color: 'blue'
    },
    {
      title: 'Environment Variable',
      description: 'Set TEST_RETRY_LOGIC=true',
      action: () => {
        navigator.clipboard.writeText('TEST_RETRY_LOGIC=true npm run dev');
        alert('Command copied to clipboard!');
      },
      color: 'green'
    },
    {
      title: 'Code Modification',
      description: 'Set ENABLE_RETRY_TESTING = true in API route',
      action: () => {
        alert('Edit app/(chat)/api/chat/route.ts and set ENABLE_RETRY_TESTING = true');
      },
      color: 'purple'
    }
  ];

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        variant="outline"
        size="sm"
        className={cn(
          'fixed bottom-4 left-4 z-50 bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100',
          className
        )}
      >
        <span className="mr-1">
          <InfoIcon size={14} />
        </span>
        Test Retry
      </Button>
    );
  }

  return (
    <div className={cn(
      'fixed bottom-4 left-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm',
      className
    )}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-gray-900">🧪 Retry Testing</h3>
        <Button
          onClick={() => setIsOpen(false)}
          variant="ghost"
          size="sm"
          className="size-6 p-0"
        >
          ×
        </Button>
      </div>
      
      <p className="text-xs text-gray-600 mb-3">
        Test retry functionality and visual indicators:
      </p>
      
      <div className="space-y-2">
        {testingMethods.map((method, index) => (
          <div key={index} className="border border-gray-100 rounded p-2">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-800">
                  {method.title}
                </h4>
                <p className="text-xs text-gray-600">
                  {method.description}
                </p>
              </div>
              {method.url ? (
                <Button
                  onClick={() => {
                    window.location.href = window.location.pathname + method.url;
                  }}
                  size="sm"
                  className="ml-2 h-7 text-xs"
                >
                  Test
                </Button>
              ) : (
                <Button
                  onClick={method.action}
                  size="sm"
                  variant="outline"
                  className="ml-2 h-7 text-xs"
                >
                  Copy
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-700">
        <strong>Expected behavior:</strong> Toast notifications, retry indicator (top-right), enhanced loading states, console logs.
      </div>
    </div>
  );
}
