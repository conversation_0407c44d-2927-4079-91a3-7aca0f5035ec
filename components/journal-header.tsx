'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useSidebar } from './ui/sidebar';
import { memo, useState } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { BookIcon } from './icons';
import { Mic, PenIcon, Menu } from 'lucide-react';
import { useSession } from 'next-auth/react';

interface JournalHeaderProps {
  id: string;
  title: string;
  isReadonly: boolean;
  isInitialSave: boolean;
  isSaving: boolean;
  onTitleChange: (title: string) => void;
  onSave: () => void;
  titleInputRef: React.RefObject<HTMLInputElement>;
}

function PureJournalHeader({
  title,
  isReadonly,
  isInitialSave,
  isSaving,
  onTitleChange,
  onSave,
  titleInputRef,
}: JournalHeaderProps) {
  const router = useRouter();
  const { toggleSidebar } = useSidebar();
  const [isEditingTitle, setIsEditingTitle] = useState(isInitialSave);
  const { data: session } = useSession();

  return (
    <>
      <header className="chat-header">
        <div className="flex flex-col justify-left">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                className="chat-header-button"
                onClick={toggleSidebar}
              >
                <Menu className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Toggle Sidebar</TooltipContent>
          </Tooltip>
        </div>
        <div className="flex flex-col items-center flex-1 justify-center">
          <span
            className="chat-header-title"
            onClick={() => {
              router.push('/');
              router.refresh();
            }}
          >
            GentleGossip
          </span>
        </div>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className="chat-header-button hidden sm:flex"
              onClick={() => {
                router.push('/');
                router.refresh();
              }}
            >
              <span>Chat</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>New Chat</TooltipContent>
        </Tooltip>
        <Button
          variant="outline"
          className="chat-header-button sm:hidden px-2"
          onClick={() => {
            router.push('/');
            router.refresh();
          }}
        >
          <span>Chat</span>
        </Button>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className="chat-header-button hidden sm:flex"
              onClick={() => {
                router.push('/talk');
                router.refresh();
              }}
            >
              <Mic className="mr-2 size-4" />
              <span>Voice</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>Voice Chat</TooltipContent>
        </Tooltip>
        <Button
          variant="outline"
          className="chat-header-button sm:hidden px-2"
          onClick={() => {
            router.push('/talk');
            router.refresh();
          }}
        >
          <Mic className="size-4" />
        </Button>
      </header>

      <div className="flex items-center justify-between p-2 sm:p-4 border-b">
        <div className="flex items-center gap-1 sm:gap-2 flex-1 overflow-hidden">
          <BookIcon size={20} />
          {isEditingTitle ? (
            <div className="flex items-center w-full">
              <input
                ref={titleInputRef}
                type="text"
                placeholder="Enter journal title..."
                className="text-base sm:text-xl font-semibold border-none focus:outline-none focus:ring-0 bg-transparent w-full"
                value={title}
                onChange={(e) => onTitleChange(e.target.value)}
                style={{ fontSize: '16px' }} /* Prevents iOS zoom on focus */
                onBlur={() => {
                  if (!isInitialSave) {
                    setIsEditingTitle(false);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if (!isInitialSave) {
                      setIsEditingTitle(false);
                    }
                    onSave();
                  }
                }}
              />
            </div>
          ) : (
            <div
              className="flex items-center cursor-pointer group w-full"
              onClick={() => !isReadonly && setIsEditingTitle(true)}
            >
              <h1 className="text-base sm:text-xl font-semibold truncate mr-2">{title || 'Journal Entry'}</h1>
              {!isReadonly && (
                <PenIcon size={14} className="opacity-0 group-hover:opacity-100 transition-opacity" />
              )}
            </div>
          )}
        </div>
        {!isReadonly && (
          <Button
            onClick={onSave}
            disabled={isSaving}
            className="chat-header-button ml-2 whitespace-nowrap text-xs sm:text-sm px-2 sm:px-4"
          >
            {isSaving ? 'Saving...' : isInitialSave ? 'Create' : 'Save'}
          </Button>
        )}
      </div>
    </>
  );
}

export const JournalHeader = memo(PureJournalHeader);
