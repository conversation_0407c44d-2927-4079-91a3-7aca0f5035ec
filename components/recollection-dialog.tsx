'use client';

import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';
import { Button } from './ui/button';
import type { Recollection } from '@/lib/db/schema';
import { useRecollectionDialog } from './recollection-dialog-context';

export function RecollectionDialog() {
  const { selectedRecollection, isDialogOpen, closeDialog } = useRecollectionDialog();

  if (!selectedRecollection) return null;

  const formatPeriod = (start: Date, end: Date, granularity: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    
    switch (granularity) {
      case 'hourly':
        return `${format(startDate, 'MMM d, yyyy h:mm a')} - ${format(endDate, 'h:mm a')}`;
      case 'daily':
        return format(startDate, 'MMMM d, yyyy');
      case 'weekly':
        return `${format(startDate, 'MMMM d')} - ${format(endDate, 'MMMM d, yyyy')}`;
      case 'monthly':
        return format(startDate, 'MMMM yyyy');
      default:
        return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
    }
  };

  const getGranularityConfig = (granularity: string) => {
    switch (granularity) {
      case 'hourly':
        return { icon: '🕐', color: 'text-blue-600', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' };
      case 'daily':
        return { icon: '📅', color: 'text-green-600', bgColor: 'bg-green-50', borderColor: 'border-green-200' };
      case 'weekly':
        return { icon: '📊', color: 'text-purple-600', bgColor: 'bg-purple-50', borderColor: 'border-purple-200' };
      case 'monthly':
        return { icon: '📈', color: 'text-orange-600', bgColor: 'bg-orange-50', borderColor: 'border-orange-200' };
      default:
        return { icon: '📝', color: 'text-gray-600', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' };
    }
  };

  const config = getGranularityConfig(selectedRecollection.granularity);

  return (
    <Dialog open={isDialogOpen} onOpenChange={closeDialog}>
      <DialogContent className="max-w-3xl max-h-[85vh] overflow-y-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-xl">
        <DialogHeader className="space-y-4">
          <div className={`flex items-start gap-3 p-4 rounded-lg border ${config.bgColor} ${config.borderColor}`}>
            <span className="text-2xl mt-0.5 shrink-0">{config.icon}</span>
            <div className="flex-1 min-w-0">
              <DialogTitle className={`text-lg font-semibold ${config.color} leading-tight`}>
                <span className="capitalize">{selectedRecollection.granularity}</span>
                <span className="text-gray-700 dark:text-gray-300 ml-1">Recollection</span>
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600 dark:text-gray-400 mt-2 leading-relaxed">
                {formatPeriod(selectedRecollection.period_start, selectedRecollection.period_end, selectedRecollection.granularity)}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>
        
        <div className="mt-6 space-y-4">
          <div className="border rounded-lg p-6 bg-gray-50/50 dark:bg-gray-800/50">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
              <span>📋</span>
              Summary
            </h3>
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <div className="whitespace-pre-wrap text-sm leading-relaxed text-gray-900 dark:text-gray-100">
                {selectedRecollection.summary}
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              This recollection covers {selectedRecollection.granularity} activity and insights.
            </div>
            <Button
              onClick={closeDialog}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
