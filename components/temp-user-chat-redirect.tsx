'use client';

import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';

export function TempUserChatRedirect() {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    // Wait for session to be loaded
    if (status === 'loading') return;

    // If user is authenticated, clear any stored temporary chat ID and don't redirect
    if (session?.user?.id) {
      localStorage.removeItem('tempUserChatId');
      localStorage.removeItem('tempUserHasMessages');
      return;
    }

    // Only for temporary users: check if there's a stored chat ID and if they have messages
    const storedChatId = localStorage.getItem('tempUserChatId');
    const hasMessages = localStorage.getItem('tempUserHasMessages') === 'true';

    // Only redirect if there's a stored chat ID AND the user has sent messages before
    // This prevents redirecting to empty chats that don't exist in the database yet
    if (storedChatId && hasMessages) {
      // Redirect to the existing chat
      router.replace(`/chat/${storedChatId}`);
    }
  }, [router, session?.user?.id, status]);

  // This component doesn't render anything visible
  return null;
}
