'use client';

import React from 'react';
import { signOut } from '@/app/(auth)/auth';

export const SignOutForm = () => {
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault(); // Prevent the default form submission behavior
    ('use server');

    await signOut({
      redirectTo: '/',
    });
  };

  return (
    <form
      className="w-full"
      onSubmit={handleSubmit} // Custom submit handler
    >
      <button
        type="submit"
        className="w-full text-left px-1 py-0.5 text-red-500"
      >
        Sign out
      </button>
    </form>
  );
};
