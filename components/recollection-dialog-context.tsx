'use client';

import React, { createContext, useContext, useState } from 'react';
import type { Recollection } from '@/lib/db/schema';

interface RecollectionDialogContextType {
  selectedRecollection: Recollection | null;
  isDialogOpen: boolean;
  openDialog: (recollection: Recollection) => void;
  closeDialog: () => void;
}

const RecollectionDialogContext = createContext<RecollectionDialogContextType | undefined>(undefined);

export function RecollectionDialogProvider({ children }: { children: React.ReactNode }) {
  const [selectedRecollection, setSelectedRecollection] = useState<Recollection | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const openDialog = (recollection: Recollection) => {
    setSelectedRecollection(recollection);
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setSelectedRecollection(null);
  };

  return (
    <RecollectionDialogContext.Provider
      value={{
        selectedRecollection,
        isDialogOpen,
        openDialog,
        closeDialog,
      }}
    >
      {children}
    </RecollectionDialogContext.Provider>
  );
}

export function useRecollectionDialog() {
  const context = useContext(RecollectionDialogContext);
  if (context === undefined) {
    throw new Error('useRecollectionDialog must be used within a RecollectionDialogProvider');
  }
  return context;
}
