'use client';
import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';
import type { ChatRequestOptions, CreateMessage, Message } from 'ai';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { useSidebar } from './ui/sidebar';
import { memo } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import type { VisibilityType, } from './visibility-selector';
import { Mic, Menu, BookOpen } from 'lucide-react';
import { ClearMessagesDialog } from './clear-messages-dialog';
import { useIsMobile } from '@/hooks/use-mobile';

function PureChatHeader({
  user, // Added user prop
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  isUserTemporary,
  append,
  onClearMessagesAction,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  isUserTemporary?: boolean;
  append?: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  user: any; // Added user prop type
  onClearMessagesAction?: () => Promise<void>;
}) {
  const router = useRouter();
  const { toggleSidebar } = useSidebar();
  const { data: session } = useSession();
  const isMobile = useIsMobile();

  const { width: windowWidth } = useWindowSize();

  return (
    <header className="chat-header">
      <div className="flex flex-col justify-left">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className={`chat-header-button ${isMobile ? 'px-2' : ''}`}
              onClick={toggleSidebar}
            >
              <Menu className="size-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Toggle Sidebar</TooltipContent>
        </Tooltip>
      </div>
      <div className="flex flex-col items-center flex-1 justify-center">
        <span
          className={`chat-header-title ${isMobile ? 'text-xl' : 'text-2xl'}`}
          onClick={() => {
            router.push('/');
            router.refresh();
          }}
        >
          GentleGossip
        </span>
      </div>
      {append && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className={`chat-header-button ${isMobile ? 'px-2' : ''}`}
              onClick={() => {
                router.push('/journal');
                router.refresh();
              }}
            >
              {isMobile ? (
                <BookOpen className="size-4" />
              ) : (
                <span>Journal</span>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>New Journal</TooltipContent>
        </Tooltip>
      )}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            className={`chat-header-button ${isMobile ? 'px-2' : ''}`}
            onClick={() => {
              router.push('/talk');
              router.refresh();
            }}
          >
            {isMobile ? (
              <Mic className="size-4" />
            ) : (
              <>
                <Mic className="mr-2 size-4" />
                <span>Voice</span>
              </>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>Voice Chat</TooltipContent>
      </Tooltip>

      {/* Clear Messages Button - show for both authenticated and temporary users */}
      {(session?.user || isUserTemporary) && onClearMessagesAction && (
        <ClearMessagesDialog onClearMessagesAction={onClearMessagesAction} />
      )}

      {/* {!isReadonly && (
        <ModelSelector
          selectedModelId={selectedModelId}
          className="order-1 md:order-2"
        />
      )} */}

      {/* {!isReadonly && (
        <VisibilitySelector
          chatId={chatId}
          selectedVisibilityType={selectedVisibilityType}
          className="order-1 md:order-3"
        />
      )} */}
    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return prevProps.selectedModelId === nextProps.selectedModelId;
});
