'use client';
import { useVoice } from '@humeai/voice-react';
import { Button } from './button';
import { Mic, MicOff, Phone, } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import { Toggle } from './toggle';
import MicFFT from './MicFFT';
import { cn } from '@/lib/utils';
import { useContext, useState, useEffect } from 'react';
import { MessageContext } from './Chat';
import CallSummaryDialog from './CallSummaryDialog';

export default function Controls() {
  const voice = useVoice();
  const { disconnect, status, isMuted, unmute, mute, micFft } = voice;
  const { chatId } = useContext(MessageContext);
  const [showSummary, setShowSummary] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [summary, setSummary] = useState<{
    duration: string;
    messageCount: number;
    emotions: Record<string, number>;
    timeline: Array<{
      timestamp: string;
      topEmotions: Array<{ emotion: string; score: number }>;
    }>;
  } | null>(null);

  // Track call start time
  const [callStartTime, setCallStartTime] = useState<Date | null>(null);

  // Set call start time when connected
  useEffect(() => {
    if (status?.value === 'connected') {
      setCallStartTime(new Date());
    }
  }, [status?.value]);

  // Fetch call summary when disconnecting
  const handleDisconnect = async () => {
    if (chatId && callStartTime) {
      // Show dialog immediately with loading state
      setShowSummary(true);
      setIsLoading(true);
      disconnect();

      try {
        // Calculate call duration
        const duration = new Date().getTime() - callStartTime.getTime();
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        const durationStr = `${minutes}m ${seconds}s`;

        // Fetch messages and emotions
        const response = await fetch(`/talk/api/summary?chatId=${chatId}`);
        const data = await response.json();

        setSummary({
          duration: durationStr,
          messageCount: data.messageCount,
          emotions: data.emotions,
          timeline: data.timeline,
        });
      } catch (error) {
        console.error('Failed to fetch call summary:', error);
      } finally {
        setIsLoading(false);
      }
    } else {
      disconnect();
    }
  };

  return (
    <>
      <div
        className={cn(
          'fixed bottom-0 left-0 w-full p-4 flex items-center justify-center',
          'bg-gradient-to-t from-card via-card/90 to-card/0',
        )}
      >
        <AnimatePresence>
          {status?.value === 'connected' ? (
            <motion.div
              initial={{
                y: '100%',
                opacity: 0,
              }}
              animate={{
                y: 0,
                opacity: 1,
              }}
              exit={{
                y: '100%',
                opacity: 0,
              }}
              className={
                'p-4 bg-white border border-zinc-400 rounded-xl shadow-md flex items-center gap-4'
              }
            >
              <Toggle
                pressed={!isMuted}
                onPressedChange={() => {
                  if (isMuted) {
                    unmute();
                  } else {
                    mute();
                  }
                }}
              >
                {isMuted ? (
                  <MicOff className={'size-6 text-red-500'} />
                ) : (
                  <Mic className={'size-6 text-green-500'} />
                )}
              </Toggle>

              <div className={'relative grid h-8 w-48 shrink grow-0'}>
                <MicFFT fft={micFft} className={'fill-current'} />
              </div>

              <Button
                className={cn(
                  'flex items-center gap-1 bg-red-500 text-white border rounded px-4 py-3.5 text-lg',
                  'transition-all duration-200',
                  'hover:bg-red-600 hover:scale-105 hover:shadow-lg',
                )}
                onClick={handleDisconnect}
                variant={'destructive'}
              >
                <span>
                  <Phone
                    className={'size-4 opacity-50'}
                    strokeWidth={2}
                    stroke={'currentColor'}
                  />
                </span>
                <span>End Call</span>
              </Button>
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>

      <CallSummaryDialog
        isOpen={showSummary}
        onClose={() => setShowSummary(false)}
        summary={summary}
        isLoading={isLoading}
      />
    </>
  );
}
