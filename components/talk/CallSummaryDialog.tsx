import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/talk/dialog';
import { Loader2 } from 'lucide-react';
import { motion, } from 'framer-motion';
import { expressionColors, isExpressionColor } from '@/lib/expressionColors';
import type { CSSProperties } from 'react';

interface CallSummaryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  summary: {
    duration: string;
    messageCount: number;
    emotions: Record<string, number>;
  } | null;
  isLoading: boolean;
}

export default function CallSummaryDialog({
  isOpen,
  onClose,
  summary,
  isLoading,
}: CallSummaryDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            <div className="gradient-primary text-white p-4 rounded-lg">
              <span className="text-2xl font-bold">Call Summary</span>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-6">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-8 gap-4">
              <Loader2 className="size-8 animate-spin text-primary" />
              <p className="text-base text-muted-foreground">
                Generating call summary...
              </p>
            </div>
          ) : summary ? (
            <>
              <div className="space-y-3">
                <h4 className="text-base font-medium">Call Duration</h4>
                <p className="text-base text-muted-foreground">
                  {summary.duration}
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="text-base font-medium">Messages Exchanged</h4>
                <p className="text-base text-muted-foreground">
                  {summary.messageCount}
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="text-base font-medium">
                  Overall Emotional Analysis
                </h4>
                <p className="text-sm text-muted-foreground mb-4">
                  Emotions are shown on a scale from 0 to 1, where higher
                  numbers indicate stronger emotions
                </p>
                <div className="space-y-3">
                  {Object.entries(summary.emotions || {}).map(
                    ([emotion, score], index) => (
                      <motion.div
                        key={emotion}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center gap-3"
                      >
                        <span className="text-sm font-medium w-24 capitalize">
                          {emotion}
                        </span>
                        <div
                          className="flex-1 h-2 relative max-w-[120px] ml-4"
                          style={
                            {
                              '--bg': isExpressionColor(emotion)
                                ? expressionColors[emotion]
                                : 'var(--bg)',
                            } as CSSProperties
                          }
                        >
                          <div className="absolute top-0 left-0 size-full rounded-full opacity-10 bg-[var(--bg)]" />
                          <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${score * 100}%` }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                            className="absolute top-0 left-0 h-full bg-[var(--bg)] rounded-full"
                          />
                        </div>
                        <div className="flex flex-col items-end min-w-[60px]">
                          <span className="text-sm font-medium">
                            {score.toFixed(2)}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {score >= 0.7
                              ? 'High'
                              : score >= 0.4
                                ? 'Med'
                                : 'Low'}
                          </span>
                        </div>
                      </motion.div>
                    ),
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 gap-4">
              <p className="text-base text-muted-foreground">
                Failed to generate call summary
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
