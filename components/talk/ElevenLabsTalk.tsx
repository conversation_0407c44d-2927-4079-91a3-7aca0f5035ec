'use client';

import { useConversation } from '@11labs/react';
import { useCallback, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, PhoneOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatusIndicatorProps {
  isActive: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'red';
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  isActive,
  size = 'md',
  color = 'blue',
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
  };

  return (
    <div className="relative flex items-center justify-center">
      <div
        className={cn(
          'rounded-full',
          sizeClasses[size],
          colorClasses[color],
          isActive && 'animate-pulse',
        )}
      />
      {isActive && (
        <div
          className={cn(
            'absolute rounded-full',
            sizeClasses[size],
            colorClasses[color],
            'opacity-75 animate-ping',
          )}
        />
      )}
    </div>
  );
};

const WaveformAnimation = () => {
  return (
    <div className="flex items-center gap-0.5 h-12">
      {[...Array(7)].map((_, i) => (
        <motion.div
          key={i}
          className="w-1 bg-blue-500 rounded-full"
          animate={{
            height: ['12px', '24px', '12px'],
          }}
          transition={{
            duration: 0.7,
            repeat: Number.POSITIVE_INFINITY,
            delay: i * 0.1,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );
};

const CallButton = ({
  isConnected,
  isConnecting,
  onClick,
}: {
  isConnected: boolean;
  isConnecting: boolean;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    disabled={isConnecting}
    className={cn(
      'w-14 h-14 rounded-full',
      'flex items-center justify-center',
      'transition-all duration-300',
      'shadow-lg',
      isConnecting
        ? 'bg-blue-500'
        : isConnected
          ? 'bg-red-500 hover:bg-red-600'
          : 'bg-green-500 hover:bg-green-600',
    )}
  >
    {isConnected ? (
      <PhoneOff className="size-6 text-white" />
    ) : (
      <Phone
        className={cn('w-6 h-6 text-white', isConnecting && 'animate-pulse')}
      />
    )}
  </button>
);

export function ElevenLabsTalk() {
  const [micStream, setMicStream] = useState<MediaStream | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const conversation = useConversation({
    onConnect: () => console.log('Connected'),
    onDisconnect: () => {
      console.log('Disconnected');
      // Clean up microphone when disconnected
      if (micStream) {
        micStream.getTracks().forEach((track) => track.stop());
        setMicStream(null);
      }
    },
    onMessage: (message: { text: string }) => console.log('Message:', message),
    onError: (error: Error) => console.error('Error:', error),
  });

  const startConversation = useCallback(async () => {
    setIsConnecting(true);
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMicStream(stream);
      await conversation.startSession({
        agentId: 'hY3vBD5nBeMZbVhhqEuJ',
      });
    } catch (error) {
      console.error('Failed to start conversation:', error);
    } finally {
      setIsConnecting(false);
    }
  }, [conversation]);

  const stopConversation = useCallback(async () => {
    try {
      await conversation.endSession();
      // Clean up microphone when stopping
      if (micStream) {
        micStream.getTracks().forEach((track) => track.stop());
        setMicStream(null);
      }
    } catch (error) {
      console.error('Failed to stop conversation:', error);
    }
  }, [conversation, micStream]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (micStream) {
        micStream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [micStream]);

  return (
    <div className="flex flex-col items-center gap-6">
      <div className="h-12">
        <AnimatePresence mode="wait">
          {conversation.status === 'connected' && conversation.isSpeaking && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <WaveformAnimation />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <CallButton
        isConnected={conversation.status === 'connected'}
        isConnecting={isConnecting}
        onClick={
          conversation.status === 'connected'
            ? stopConversation
            : startConversation
        }
      />

      <AnimatePresence mode="wait">
        {(conversation.status === 'connected' || isConnecting) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="flex items-center gap-2 px-4 py-2 rounded-full border border-gray-200"
          >
            {isConnecting ? (
              <span className="text-blue-500 flex items-center gap-2 text-sm font-medium">
                <StatusIndicator isActive={true} size="sm" color="blue" />
                Connecting...
              </span>
            ) : conversation.isSpeaking ? (
              <span className="text-blue-500 flex items-center gap-2 text-sm font-medium">
                <StatusIndicator isActive={true} size="sm" color="blue" />
                Speaking
              </span>
            ) : (
              <span className="text-green-500 flex items-center gap-2 text-sm font-medium">
                <StatusIndicator isActive={true} size="sm" color="green" />
                Listening
              </span>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
