'use client';
import { cn } from '@/lib/utils';
import { useVoice } from '@humeai/voice-react';
import { AnimatePresence, motion } from 'framer-motion';
import { type ComponentRef, forwardRef } from 'react';

// Define types for Hume messages and emotions
type EmotionScores = {
  [key: string]: number;
};

type HumeMessage = {
  type: string;
  message: {
    id?: string;
    role: string;
    content: string;
  };
  models: {
    prosody?: {
      scores: EmotionScores;
    };
  };
};

const Messages = forwardRef<
  ComponentRef<typeof motion.div>,
  Record<never, never>
>(function Messages(_, ref) {
  const { messages } = useVoice();

  return (
    <motion.div
      layoutScroll
      className={'grow rounded-md overflow-auto p-4'}
      ref={ref}
    >
      <motion.div
        className={'max-w-2xl mx-auto w-full flex flex-col gap-4 pb-24'}
      >
        <AnimatePresence mode={'popLayout'}>
          {(messages as unknown as HumeMessage[]).map((msg, index) => {
            if (
              msg.type === 'user_message' ||
              msg.type === 'assistant_message'
            ) {
              return (
                <motion.div
                  key={msg.type + index}
                  className={cn(
                    'w-[80%]',
                    msg.type === 'user_message'
                      ? 'gradient-primary text-white'
                      : 'bg-white',
                    'border border-zinc-400 rounded-xl shadow-md',
                    msg.type === 'user_message' ? 'ml-auto' : '',
                  )}
                  initial={{
                    opacity: 0,
                    y: 10,
                  }}
                  animate={{
                    opacity: 1,
                    y: 0,
                  }}
                  exit={{
                    opacity: 0,
                    y: 0,
                  }}
                >
                  <div
                    className={cn(
                      'text-xs capitalize font-medium opacity-50 leading-none pt-4 px-3',
                    )}
                  >
                    {msg.message.role}
                  </div>
                  <div className={'pb-3 px-3'}>{msg.message.content}</div>
                </motion.div>
              );
            }

            return null;
          })}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
});

export default Messages;
