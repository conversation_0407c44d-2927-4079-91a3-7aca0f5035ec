import { useVoice } from '@humeai/voice-react';
import { AnimatePresence, motion } from 'framer-motion';
import { Button } from './button';
import { Phone } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useContext, useState } from 'react';
import { MessageContext } from './Chat';

export default function StartCall() {
  const { status, connect } = useVoice();
  const [isLoading, setIsLoading] = useState(false);
  const { createNewChat } = useContext(MessageContext);

  return (
    <AnimatePresence>
      {status.value !== 'connected' ? (
        <motion.div
          className={
            'absolute inset-0 p-4 flex items-center justify-center bg-background'
          }
          initial="initial"
          animate="enter"
          exit="exit"
          variants={{
            initial: { opacity: 0 },
            enter: { opacity: 1 },
            exit: { opacity: 0 },
          }}
        >
          <AnimatePresence>
            <motion.div
              variants={{
                initial: { scale: 0.5 },
                enter: { scale: 1 },
                exit: { scale: 0.5 },
              }}
            >
              <Button
                className={cn(
                  'z-50 flex items-center gap-1.5 bg-teal-500 text-white border shadow-md rounded px-4 py-3.5 text-lg',
                  'transition-all duration-200',
                  'hover:bg-teal-600 hover:scale-105 hover:shadow-lg',
                  isLoading && 'opacity-70 cursor-not-allowed',
                )}
                onClick={async () => {
                  setIsLoading(true);
                  try {
                    // Create a new chat before connecting
                    await createNewChat();
                    // Then connect to the voice service
                    await connect();
                  } catch (error) {
                    console.error('Failed to start call:', error);
                  } finally {
                    setIsLoading(false);
                  }
                }}
                disabled={isLoading}
              >
                <span>
                  <Phone
                    className={'size-4 opacity-50'}
                    strokeWidth={2}
                    stroke={'currentColor'}
                  />
                </span>
                <span>{isLoading ? 'Starting...' : 'Start Call'}</span>
              </Button>
            </motion.div>
          </AnimatePresence>
        </motion.div>
      ) : null}
    </AnimatePresence>
  );
}
