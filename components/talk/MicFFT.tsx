'use client';

import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { useRef, useEffect, useState } from 'react';

export default function MicFFT({
  fft,
  className,
}: {
  fft: number[];
  className?: string;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          width: containerRef.current.clientWidth,
          height: containerRef.current.clientHeight,
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const { width, height } = dimensions;

  return (
    <div ref={containerRef} className={cn('relative size-full', className)}>
      {width > 0 && height > 0 && (
        <motion.svg
          viewBox={`0 0 ${width} ${height}`}
          width={width}
          height={height}
          className="absolute inset-0 size-full"
        >
          {Array.from({ length: 24 }).map((_, index) => {
            const value = (fft[index] ?? 0) / 4;
            const h = Math.min(Math.max(height * value, 2), height);
            const yOffset = height * 0.5 - h * 0.5;

            return (
              <motion.rect
                key={`mic-fft-${index}`}
                height={h}
                width={2}
                x={2 + (index * width - 4) / 24}
                y={yOffset}
                rx={4}
              />
            );
          })}
        </motion.svg>
      )}
    </div>
  );
}
