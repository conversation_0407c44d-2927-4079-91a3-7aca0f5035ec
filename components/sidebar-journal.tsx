'use client';

import {
  isToday,
  isYesterday,
  subMonths,
  subWeeks,
  formatDistanceToNow,
} from 'date-fns';
import Link from 'next/link';
import { useParams, usePathname, useRouter } from 'next/navigation';
import type { User } from 'next-auth';
import { memo, useEffect, useState, useMemo } from 'react';
import { toast } from 'sonner';
import useSWR from 'swr';

import {
  MoreHorizontalIcon,
  TrashIcon,
  BookIcon,
} from '@/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import type { Document } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';

type GroupedDocuments = {
  today: Document[];
  yesterday: Document[];
  lastWeek: Document[];
  lastMonth: Document[];
  older: Document[];
};

const PureJournalItem = ({
  document,
  isActive,
  onDelete,
  setOpenMobile,
}: {
  document: Document;
  isActive: boolean;
  onDelete: (documentId: string) => void;
  setOpenMobile: (open: boolean) => void;
}) => {
  return (
    <SidebarMenuItem className="my-2">
      <SidebarMenuButton
        asChild
        isActive={isActive}
        className="text-black gradient-secondary rounded h-20 sm:h-20 p-2 touch-manipulation"
      >
        <Link href={`/journal/${document.id}`} onClick={() => setOpenMobile(false)}>
          <div className="flex flex-col w-full">
            <span className="text-sm sm:text-md font-semibold py-1 sm:py-2">
              {document.title}
            </span>
            <span className="text-xs text-zinc-700">
              {(() => {
                try {
                  return formatDistanceToNow(new Date(document.createdAt), {
                    addSuffix: true,
                  });
                } catch (error) {
                  console.error('Error formatting date:', error);
                  return 'Date unavailable';
                }
              })()}
            </span>
          </div>
        </Link>
      </SidebarMenuButton>

      <DropdownMenu modal={true}>
        <DropdownMenuTrigger asChild>
          <SidebarMenuAction
            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground mr-0.5 p-2 touch-manipulation"
            showOnHover={false} // Always show on mobile
          >
            <MoreHorizontalIcon />
            <span className="sr-only">More</span>
          </SidebarMenuAction>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          side="bottom"
          align="end"
          className="bg-white rounded shadow-md border-none min-w-[150px] z-50"
        >
          <DropdownMenuItem
            className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500 p-3 text-sm"
            onSelect={() => onDelete(document.id)}
          >
            <TrashIcon />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  );
};

export const JournalItem = memo(PureJournalItem, (prevProps, nextProps) => {
  if (prevProps.isActive !== nextProps.isActive) return false;
  return true;
});

export function SidebarJournal({ user }: { user: User | undefined }) {
  const { setOpenMobile } = useSidebar();
  const { id } = useParams();
  const pathname = usePathname();
  const {
    data: documents,
    isLoading,
    mutate,
  } = useSWR<Array<Document>>(user ? '/api/journal' : null, fetcher, {
    fallbackData: [],
  });

  useEffect(() => {
    mutate();
  }, [pathname, mutate]);

  // Get only the latest document for each unique ID
  const uniqueDocuments = useMemo(() => {
    if (!documents || documents.length === 0) return [];

    // Group documents by ID
    const documentsById = documents.reduce((acc, doc) => {
      if (!acc[doc.id]) {
        acc[doc.id] = [];
      }
      acc[doc.id].push(doc);
      return acc;
    }, {} as Record<string, Document[]>);

    // For each ID, get the document with the most recent timestamp
    return Object.values(documentsById).map(docs => {
      return docs.reduce((latest, current) => {
        const latestDate = new Date(latest.createdAt);
        const currentDate = new Date(current.createdAt);
        return currentDate > latestDate ? current : latest;
      });
    });
  }, [documents]);

  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const router = useRouter();
  const handleDelete = async () => {
    const deletePromise = fetch(`/api/document?id=${deleteId}`, {
      method: 'DELETE',
    });

    toast.promise(deletePromise, {
      loading: 'Deleting journal entry...',
      success: () => {
        mutate();
        return 'Journal entry deleted successfully';
      },
      error: 'Failed to delete journal entry',
    });

    setShowDeleteDialog(false);

    if (deleteId === id) {
      router.push('/journal');
    }
  };

  if (!user) {
    return (
      <SidebarGroup>
        <SidebarGroupContent>
          <div className="text-zinc-500 w-full flex flex-row justify-center items-center text-xs sm:text-sm gap-2 p-2 sm:p-4 text-center">
            <div>Login to save and revisit your journal entries!</div>
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  if (isLoading) {
    return (
      <SidebarGroup>
        <div className="px-2 py-1 text-xs text-sidebar-foreground/50">
          Today
        </div>
        <SidebarGroupContent>
          <div className="flex flex-col">
            {[44, 32, 28, 64, 52].map((item) => (
              <div
                key={item}
                className="rounded-md h-8 flex gap-2 px-2 items-center"
              >
                <div
                  className="h-4 rounded-md flex-1 max-w-[--skeleton-width] bg-sidebar-accent-foreground/10"
                  style={
                    {
                      '--skeleton-width': `${item}%`,
                    } as React.CSSProperties
                  }
                />
              </div>
            ))}
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  // If no documents or all filtered out
  if (!documents || documents.length === 0 || uniqueDocuments.length === 0) {
    return (
      <SidebarGroup>
        <SidebarGroupContent>
          <div className="text-zinc-500 w-full flex flex-col sm:flex-row justify-center items-center text-xs sm:text-sm gap-2 p-4 text-center">
            <BookIcon size={16} />
            <div>
              Your journal entries will appear here once you start writing!
            </div>
          </div>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  }

  const groupedDocuments = groupDocumentsByDate(uniqueDocuments);

  return (
    <>
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu>
            {groupedDocuments.today.length > 0 && (
              <>
                <div className="px-2 py-1 text-xs text-sidebar-foreground/50">
                  Today
                </div>
                {groupedDocuments.today.map((document) => (
                  <JournalItem
                    key={`${document.id}-${document.createdAt.toString()}`}
                    document={document}
                    isActive={document.id === id}
                    onDelete={(documentId) => {
                      setDeleteId(documentId);
                      setShowDeleteDialog(true);
                    }}
                    setOpenMobile={setOpenMobile}
                  />
                ))}
              </>
            )}

            {groupedDocuments.yesterday.length > 0 && (
              <>
                <div className="px-2 py-1 text-xs text-sidebar-foreground/50 mt-6">
                  Yesterday
                </div>
                {groupedDocuments.yesterday.map((document) => (
                  <JournalItem
                    key={`${document.id}-${document.createdAt.toString()}`}
                    document={document}
                    isActive={document.id === id}
                    onDelete={(documentId) => {
                      setDeleteId(documentId);
                      setShowDeleteDialog(true);
                    }}
                    setOpenMobile={setOpenMobile}
                  />
                ))}
              </>
            )}

            {groupedDocuments.lastWeek.length > 0 && (
              <>
                <div className="px-2 py-1 text-xs text-sidebar-foreground/50 mt-6">
                  Last 7 days
                </div>
                {groupedDocuments.lastWeek.map((document) => (
                  <JournalItem
                    key={`${document.id}-${document.createdAt.toString()}`}
                    document={document}
                    isActive={document.id === id}
                    onDelete={(documentId) => {
                      setDeleteId(documentId);
                      setShowDeleteDialog(true);
                    }}
                    setOpenMobile={setOpenMobile}
                  />
                ))}
              </>
            )}

            {groupedDocuments.lastMonth.length > 0 && (
              <>
                <div className="px-2 py-1 text-xs text-sidebar-foreground/50 mt-6">
                  Last 30 days
                </div>
                {groupedDocuments.lastMonth.map((document) => (
                  <JournalItem
                    key={`${document.id}-${document.createdAt.toString()}`}
                    document={document}
                    isActive={document.id === id}
                    onDelete={(documentId) => {
                      setDeleteId(documentId);
                      setShowDeleteDialog(true);
                    }}
                    setOpenMobile={setOpenMobile}
                  />
                ))}
              </>
            )}

            {groupedDocuments.older.length > 0 && (
              <>
                <div className="px-2 py-1 text-xs text-sidebar-foreground/50 mt-6">
                  Older
                </div>
                {groupedDocuments.older.map((document) => (
                  <JournalItem
                    key={`${document.id}-${document.createdAt.toString()}`}
                    document={document}
                    isActive={document.id === id}
                    onDelete={(documentId) => {
                      setDeleteId(documentId);
                      setShowDeleteDialog(true);
                    }}
                    setOpenMobile={setOpenMobile}
                  />
                ))}
              </>
            )}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="bg-white rounded shadow-md border-none max-w-[90vw] sm:max-w-md p-4 sm:p-6">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-lg sm:text-xl">Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription className="text-sm sm:text-base">
              This action cannot be undone. This will permanently delete your
              journal entry and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0 mt-4">
            <AlertDialogCancel className="mt-2 sm:mt-0 w-full sm:w-auto">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-500 text-white hover:bg-red-600 w-full sm:w-auto"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

function groupDocumentsByDate(docs: Document[]): GroupedDocuments {
  const now = new Date();
  const oneWeekAgo = subWeeks(now, 1);
  const oneMonthAgo = subMonths(now, 1);

  const groups = docs.reduce(
    (groups, doc) => {
      const docDate = new Date(doc.createdAt);

      if (isToday(docDate)) {
        groups.today.push(doc);
      } else if (isYesterday(docDate)) {
        groups.yesterday.push(doc);
      } else if (docDate > oneWeekAgo) {
        groups.lastWeek.push(doc);
      } else if (docDate > oneMonthAgo) {
        groups.lastMonth.push(doc);
      } else {
        groups.older.push(doc);
      }

      return groups;
    },
    {
      today: [],
      yesterday: [],
      lastWeek: [],
      lastMonth: [],
      older: [],
    } as GroupedDocuments,
  );

  // Sort each group with newest first (for sidebar display)
  const sortByNewestFirst = (a: Document, b: Document) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();

  groups.today.sort(sortByNewestFirst);
  groups.yesterday.sort(sortByNewestFirst);
  groups.lastWeek.sort(sortByNewestFirst);
  groups.lastMonth.sort(sortByNewestFirst);
  groups.older.sort(sortByNewestFirst);

  return groups;
}
