'use client';

import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface ClearMessagesDialogProps {
  onClearMessagesAction: () => Promise<void>;
}

export function ClearMessagesDialog({ onClearMessagesAction }: ClearMessagesDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const isMobile = useIsMobile();

  const handleClearMessages = async () => {
    setIsClearing(true);
    try {
      await onClearMessagesAction();
      setIsOpen(false);
      toast.success('Chat cleared successfully', {
        description: 'AI memory is still intact for context.',
      });
    } catch (error) {
      console.error('Error clearing messages:', error);
      toast.error('Failed to clear messages', {
        description: 'Please try again later.',
      });
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            onClick={() => setIsOpen(true)}
            className={`chat-header-button bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700 ${isMobile ? 'px-2' : ''}`}
          >
            {isMobile ? (
              <Trash2 className="size-4" />
            ) : (
              <>
                <Trash2 className="mr-2 size-4" />
                Clear
              </>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>Clear Messages</TooltipContent>
      </Tooltip>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-lg">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-gray-900 dark:text-gray-100">
              Clear Chat Messages?
            </AlertDialogTitle>
            <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <p>
                This will hide all messages from your chat interface.
              </p>
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                <p className="text-amber-800 dark:text-amber-200 font-medium">
                  ⚠️ Messages remain in AI memory
                </p>
                <p className="text-amber-700 dark:text-amber-300 mt-1">
                  Only the visual interface will be cleared.
                </p>
              </div>
              <p className="text-xs">
                This action cannot be undone.
              </p>
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isClearing} className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleClearMessages}
              disabled={isClearing}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600 text-white"
            >
              {isClearing ? 'Clearing...' : 'Clear Messages'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
