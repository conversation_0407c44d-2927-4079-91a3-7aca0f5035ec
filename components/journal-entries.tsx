import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import type { Document } from '@/lib/db/schema';
import { BookIcon } from './icons';

interface JournalEntriesProps {
  documents: Array<Document>;
  isUserTemporary?: boolean;
}

function JournalOverview() {
  return (
    <div className="mx-auto max-w-3xl px-4 py-8">
      <div className="rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8 text-center">
        <div className="mx-auto mb-4 text-blue-600">
          <BookIcon size={48} />
        </div>
        <h2 className="mb-2 text-2xl font-bold text-gray-900">
          Welcome to your Journal
        </h2>
        <p className="text-gray-600 mb-4">
          Capture your thoughts, ideas, and experiences. Your personal space for reflection and creativity.
        </p>
        <Link
          href="/journal-new"
          className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <span className="mr-2">
            <BookIcon size={16} />
          </span>
          Start Writing
        </Link>
      </div>
    </div>
  );
}

function JournalEntry({ document }: { document: Document }) {
  // Extract plain text from HTML content for preview
  const getTextPreview = (htmlContent: string | null) => {
    if (!htmlContent) return 'No content';
    
    // Simple HTML tag removal for preview
    const textContent = htmlContent.replace(/<[^>]*>/g, '').trim();
    return textContent.length > 150 
      ? textContent.substring(0, 150) + '...' 
      : textContent || 'No content';
  };

  return (
    <Link href={`/journal/${document.id}`}>
      <div className="group mx-auto max-w-3xl px-4 py-3">
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all hover:border-gray-300 hover:shadow-md">
          <div className="flex items-start gap-3">
            <div className="mt-1 shrink-0 text-blue-600">
              <BookIcon size={20} />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                {document.title}
              </h3>
              <p className="mt-2 text-sm text-gray-600 line-clamp-3">
                {getTextPreview(document.content)}
              </p>
              <div className="mt-3 flex items-center gap-4 text-xs text-gray-500">
                <span>
                  {formatDistanceToNow(new Date(document.createdAt), {
                    addSuffix: true,
                  })}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

export function JournalEntries({ documents, isUserTemporary }: JournalEntriesProps) {
  return (
    <div className="flex flex-col min-w-0 gap-2 flex-1 pt-4">
      {isUserTemporary && (
        <div className="sticky top-0 z-10 bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4 mx-4">
          <div className="flex">
            <div className="flex-1">
              <p className="text-sm text-yellow-700">
                Sign in to save your journal entries and access all features.{' '}
                <a
                  href="/login"
                  className="font-medium text-yellow-700 underline hover:text-yellow-600"
                >
                  Login
                </a>{' '}
                or{' '}
                <a
                  href="/register"
                  className="font-medium text-yellow-700 underline hover:text-yellow-600"
                >
                  Sign Up
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      {documents.length === 0 && <JournalOverview />}

      {documents.map((document) => (
        <JournalEntry key={`${document.id}-${document.createdAt}`} document={document} />
      ))}

      <div className="shrink-0 min-w-[24px] min-h-[24px]" />
    </div>
  );
}
