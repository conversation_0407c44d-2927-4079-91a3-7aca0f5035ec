'use client';

import { useState } from 'react';
import { Input } from './ui/input';
import { Label } from './ui/label';

export function AuthForm({
  action,
  children,
  defaultEmail = '',
  hidePassword = false,
  hideEmail = false,
}: {
  action: (formData: FormData) => Promise<void>;
  children: React.ReactNode;
  defaultEmail?: string;
  hidePassword?: boolean;
  hideEmail?: boolean;
}) {
  const [isPending, setIsPending] = useState(false);

  const handleSubmit = async (formData: FormData) => {
    setIsPending(true);
    try {
      await action(formData);
    } finally {
      setIsPending(false);
    }
  };

  return (
    <form
      onSubmit={async (e) => {
        e.preventDefault();
        await handleSubmit(new FormData(e.currentTarget));
      }}
      className="flex flex-col gap-4 px-4 sm:px-16"
    >
      <div className="flex flex-col gap-2">
        {!hideEmail && (
          <>
            <Label
              htmlFor="email"
              className="text-zinc-600 font-normal dark:text-zinc-400"
            >
              Email Address
            </Label>

            <Input
              id="email"
              name="email"
              className="bg-muted text-md md:text-sm"
              type="email"
              placeholder="<EMAIL>"
              autoComplete="email"
              required
              autoFocus
              defaultValue={defaultEmail}
              disabled={isPending}
            />
          </>
        )}
      </div>

      {!hidePassword && (
        <div className="flex flex-col gap-2">
          <Label
            htmlFor="password"
            className="text-zinc-600 font-normal dark:text-zinc-400"
          >
            Password
          </Label>

          <Input
            id="password"
            name="password"
            className="bg-muted text-md md:text-sm"
            type="password"
            required
            disabled={isPending}
          />
        </div>
      )}

      {children}
    </form>
  );
}
