'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import useS<PERSON> from 'swr';
import Link from 'next/link';
import { Plus } from 'lucide-react';

import { JournalHeader } from '@/components/journal-header';
import { JournalEntries } from '@/components/journal-entries';
import { Button } from '@/components/ui/button';
import type { Document } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';

export function JournalFeed({
  initialDocuments,
  isUserTemporary,
}: {
  initialDocuments: Array<Document>;
  isUserTemporary: boolean;
}) {
  const [documents, setDocuments] = useState<Array<Document>>(initialDocuments);
  const [loadingMore, setLoadingMore] = useState(false);
  const documentsContainerRef = useRef<HTMLDivElement>(null);

  // Fetch documents with SWR for real-time updates
  const { data: freshDocuments, mutate } = useSWR<Array<Document>>(
    '/api/journal',
    fetcher,
    {
      fallbackData: initialDocuments,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  // Update documents when fresh data arrives
  useEffect(() => {
    if (freshDocuments) {
      setDocuments(freshDocuments);
    }
  }, [freshDocuments]);

  // Scroll to bottom on initial load to show newest entries
  useEffect(() => {
    const container = documentsContainerRef.current;
    const hasDocuments = documents.length > 0;
    if (container && hasDocuments) {
      // Scroll to bottom on initial load
      requestAnimationFrame(() => {
        container.scrollTop = container.scrollHeight;
      });
    }
  }, [documents.length]); // Include documents.length dependency and extract complex expression

  // Auto-scroll to bottom when initial documents are loaded
  useEffect(() => {
    const container = documentsContainerRef.current;
    const hasDocuments = documents.length > 0;
    if (container && hasDocuments) {
      // Use setTimeout to ensure DOM is fully rendered
      setTimeout(() => {
        container.scrollTop = container.scrollHeight;
      }, 100);
    }
  }, [documents.length]);

  // Scroll to bottom when new documents are added (newest entries appear at bottom)
  useEffect(() => {
    const container = documentsContainerRef.current;
    if (container && documents.length > 0) {
      // Check if user is near the bottom (within 100px) before auto-scrolling
      const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 100;

      if (isNearBottom) {
        // Use requestAnimationFrame for smooth scrolling to show new entries
        requestAnimationFrame(() => {
          container.scrollTop = container.scrollHeight;
        });
      }
    }
  }, [documents]);

  const loadMoreDocuments = useCallback(async () => {
    if (loadingMore) return;

    setLoadingMore(true);
    try {
      const container = documentsContainerRef.current;
      if (!container) return;

      // Store the current scroll height before loading new documents
      const prevScrollHeight = container.scrollHeight;

      // For now, we'll just refresh the data since we don't have pagination yet
      // In the future, you could implement pagination similar to the chat messages
      await mutate();

      // After the new documents are rendered, adjust scroll position if needed
      requestAnimationFrame(() => {
        if (container) {
          const newScrollHeight = container.scrollHeight;
          const addedHeight = newScrollHeight - prevScrollHeight;
          if (addedHeight > 0) {
            container.scrollTop = addedHeight;
          }
        }
      });
    } catch (error) {
      console.error('Error loading more documents:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [loadingMore, mutate]);

  useEffect(() => {
    const container = documentsContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (container.scrollTop === 0 && !loadingMore) {
        loadMoreDocuments();
      }
    };

    container.addEventListener('scroll', handleScroll);

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [loadMoreDocuments, loadingMore]); // Include loadingMore dependency

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      <JournalHeader
        id=""
        title=""
        isReadonly={true}
        isInitialSave={false}
        isSaving={false}
        onTitleChange={() => {}}
        onSave={() => {}}
        titleInputRef={{ current: null }}
      />

      <div className="flex-1 overflow-y-auto" ref={documentsContainerRef}>
        {loadingMore && (
          <div className="flex justify-center py-2 text-gray-500">
            Loading more journal entries...
          </div>
        )}
        <JournalEntries
          documents={documents}
          isUserTemporary={isUserTemporary}
        />
      </div>

      <div className="shrink-0 bg-white border-t">
        <div className="flex mx-auto p-4 gap-2 w-full max-w-3xl justify-center">
          <Link href="/journal-new">
            <Button className="flex items-center gap-2">
              <Plus size={16} />
              New Journal Entry
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
