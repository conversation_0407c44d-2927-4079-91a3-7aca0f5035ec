'use client';

import { SidebarProvider } from '@/components/ui/sidebar';
import { ChatHeader } from './chat-header';
import type { VisibilityType } from './visibility-selector';

interface NavbarProps {
  chatId?: string;
  selectedModelId?: string;
  selectedVisibilityType?: VisibilityType;
  isReadonly?: boolean;
}

export function Navbar({
  selectedModelId = '',
  isReadonly = false,
}: NavbarProps) {
  return (
    <SidebarProvider>
      <nav className="border-b">
        <ChatHeader
          user={null}
          chatId=""
          selectedModelId={selectedModelId}
          selectedVisibilityType="private"
          isReadonly={isReadonly}
          isUserTemporary={false}
        />
      </nav>
    </SidebarProvider>
  );
}
