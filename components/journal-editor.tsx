'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { JournalHeader } from './journal-header';
import TiptapEditor from './tiptap-editor';

interface JournalEditorProps {
  id: string;
  initialContent: string;
  initialTitle?: string;
  isReadonly: boolean;
  isUserTemporary: boolean;
}

export function JournalEditor({
  id,
  initialContent,
  initialTitle = '',
  isReadonly,
  isUserTemporary,
}: JournalEditorProps) {
  const router = useRouter();
  const [content, setContent] = useState(initialContent);
  const [title, setTitle] = useState(initialTitle);
  const [isSaving, setIsSaving] = useState(false);
  const [isInitialSave, setIsInitialSave] = useState(!initialContent);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const titleInputRef = useRef<HTMLInputElement>(null);
  const lastSavedContentRef = useRef(initialContent);
  const lastSavedTitleRef = useRef(initialTitle);

  useEffect(() => {
    if ((isInitialSave || title === '') && titleInputRef.current) {
      titleInputRef.current.focus();
    }
  }, [isInitialSave, title]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autosaveTimeoutRef.current) {
        clearTimeout(autosaveTimeoutRef.current);
      }
    };
  }, []);

  const saveJournal = async (isAutoSave = false) => {
    if (!title && isInitialSave) {
      toast.error('Please enter a title for your journal entry');
      return;
    }

    // Don't save if content and title haven't changed
    if (
      content === lastSavedContentRef.current &&
      title === lastSavedTitleRef.current
    ) {
      setHasUnsavedChanges(false);
      return;
    }

    try {
      if (isAutoSave) {
        setIsAutoSaving(true);
      } else {
        setIsSaving(true);
      }

      // Make sure we have valid HTML content
      const contentToSave = content || '<p></p>';

      const response = await fetch(`/api/document?id=${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: contentToSave,
          title: title || 'Untitled Journal Entry',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save journal entry');
      }

      // Update refs with the latest saved content
      lastSavedContentRef.current = contentToSave;
      lastSavedTitleRef.current = title;
      setHasUnsavedChanges(false);

      if (isInitialSave) {
        router.push(`/journal/${id}`);
        setIsInitialSave(false);
      } else if (!isAutoSave) {
        toast.success('Journal entry saved');
      }
    } catch (error) {
      console.error('Error saving journal entry:', error);
      if (!isAutoSave) {
        toast.error('Failed to save journal entry');
      }
    } finally {
      if (isAutoSave) {
        setIsAutoSaving(false);
      } else {
        setIsSaving(false);
      }
    }
  };

  // Reference to store the timeout ID for debouncing
  const autosaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to trigger autosave after delay
  const debouncedSaveJournal = () => {
    // Clear any existing timeout to cancel previous autosave
    if (autosaveTimeoutRef.current) {
      clearTimeout(autosaveTimeoutRef.current);
    }

    // Set a new timeout for 10 seconds
    autosaveTimeoutRef.current = setTimeout(() => {
      if (!isInitialSave && hasUnsavedChanges && !isReadonly) {
        saveJournal(true);
      }
    }, 10000); // 10 seconds delay
  };

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      <JournalHeader
        id={id}
        title={title}
        isReadonly={isReadonly}
        isInitialSave={isInitialSave}
        isSaving={isSaving || isAutoSaving}
        onTitleChange={(newTitle) => {
          setTitle(newTitle);
          if (newTitle !== lastSavedTitleRef.current) {
            setHasUnsavedChanges(true);
            debouncedSaveJournal();
          }
        }}
        onSave={() => saveJournal(false)}
        titleInputRef={titleInputRef}
      />

      {isUserTemporary && (
        <div className="sticky top-0 z-10 bg-yellow-50 border-l-4 border-yellow-400 p-2 sm:p-4 mb-2 sm:mb-4">
          <div className="flex">
            <div className="flex-1">
              <p className="text-xs sm:text-sm text-yellow-700">
                Sign in to save your journal entries, access features like audio,
                and more.{' '}
                <a
                  href="/login"
                  className="font-medium text-yellow-700 underline hover:text-yellow-600"
                >
                  Login
                </a>{' '}
                or{' '}
                <a
                  href="/register"
                  className="font-medium text-yellow-700 underline hover:text-yellow-600"
                >
                  Sign Up
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex-1 overflow-y-auto p-2 sm:p-4">
        <div className="max-w-3xl mx-auto h-full">
          <TiptapEditor
            content={content}
            onChange={(newContent: string) => {
              setContent(newContent);
              if (newContent !== lastSavedContentRef.current) {
                setHasUnsavedChanges(true);
                debouncedSaveJournal();
              }
            }}
            disabled={isReadonly}
            autofocus={isInitialSave}
            className="bg-background"
          />
        </div>
      </div>
    </div>
  );
}
