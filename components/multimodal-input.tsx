'use client';

import type {
  Attachment,
  ChatRequestOptions,
  CreateMessage,
  Message,
} from 'ai';
import cx from 'classnames';
import type React from 'react';
import {
  useRef,
  useEffect,
  useState,
  useCallback,
  type Dispatch,
  type SetStateAction,
  type ChangeEvent,
  memo,
} from 'react';
import { toast } from 'sonner';
import { useLocalStorage } from 'usehooks-ts';

import { sanitizeUIMessages } from '@/lib/utils';

import { ArrowUpIcon, PaperclipIcon, StopIcon } from './icons';
import { PreviewAttachment } from './preview-attachment';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { SuggestedActions } from './suggested-actions';
import equal from 'fast-deep-equal';

function PureMultimodalInput({
  chatId,
  input,
  setInput,
  isLoading,
  stop,
  attachments,
  setAttachments,
  messages,
  setMessages,
  append,
  handleSubmit,
  className,
  isUserTemporary,
}: {
  chatId: string;
  input: string;
  setInput: (value: string) => void;
  isLoading: boolean;
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
  messages: Array<Message>;
  setMessages: Dispatch<SetStateAction<Array<Message>>>;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions,
  ) => void;
  className?: string;
  isUserTemporary?: boolean;
}) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Use a more efficient way to detect mobile vs desktop
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Check initially
    checkIsMobile();

    // Add throttled resize listener
    let timeoutId: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(checkIsMobile, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeoutId);
    };
  }, []);

  const adjustHeight = useCallback(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, []);

  useEffect(() => {
    if (textareaRef.current) {
      adjustHeight();
    }
  }, [adjustHeight]);

  // Debounced height adjustment to prevent excessive DOM manipulations
  const debouncedAdjustHeightRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedAdjustHeight = useCallback(() => {
    if (debouncedAdjustHeightRef.current) {
      clearTimeout(debouncedAdjustHeightRef.current);
    }
    debouncedAdjustHeightRef.current = setTimeout(() => {
      adjustHeight();
    }, 50); // 50ms delay
  }, [adjustHeight]);

  const [localStorageInput, setLocalStorageInput] = useLocalStorage(
    'input',
    '',
  );

  useEffect(() => {
    if (textareaRef.current) {
      const domValue = textareaRef.current.value;
      // Prefer DOM value over localStorage to handle hydration
      const finalValue = domValue || localStorageInput || '';
      setInput(finalValue);
      adjustHeight();
    }
    // Only run once after hydration
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Debounced localStorage update to prevent excessive writes
  const debouncedSetLocalStorageInputRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedSetLocalStorageInput = useCallback((value: string) => {
    if (debouncedSetLocalStorageInputRef.current) {
      clearTimeout(debouncedSetLocalStorageInputRef.current);
    }
    debouncedSetLocalStorageInputRef.current = setTimeout(() => {
      setLocalStorageInput(value);
    }, 300); // 300ms delay
  }, [setLocalStorageInput]);

  useEffect(() => {
    debouncedSetLocalStorageInput(input);

    // Cleanup on unmount
    return () => {
      if (debouncedSetLocalStorageInputRef.current) {
        clearTimeout(debouncedSetLocalStorageInputRef.current);
      }
    };
  }, [input, debouncedSetLocalStorageInput]);

  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(event.target.value);
    // Use requestAnimationFrame for non-blocking height adjustment
    requestAnimationFrame(() => {
      adjustHeight();
    });
  };

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);

  const submitForm = useCallback(() => {
    // Check if we're on the main chat page (not a specific chat)
    const isMainChatPage = window.location.pathname === '/chat' || window.location.pathname === '/';

    // Only apply special logic for temporary users
    if (isUserTemporary && isMainChatPage && messages.length === 0) {
      // For temporary users' first message on main chat page, store the chat ID and update URL
      localStorage.setItem('tempUserChatId', chatId);
      window.history.replaceState({}, '', `/chat/${chatId}`);
    } else if (!isUserTemporary && !isMainChatPage) {
      // For logged-in users on specific chat pages, just update the URL
      window.history.replaceState({}, '', `/chat/${chatId}`);
    }
    // For logged-in users on main chat page, don't change the URL - keep them on /

    handleSubmit(undefined, {
      experimental_attachments: attachments,
    });

    setAttachments([]);
    setLocalStorageInput('');

    if (!isMobile) {
      textareaRef.current?.focus();
    } else {
      // Blur the textarea on mobile to close the keyboard
      textareaRef.current?.blur();
    }

    if (textareaRef.current) {
      adjustHeight();
    }
  }, [
    attachments,
    handleSubmit,
    setAttachments,
    setLocalStorageInput,
    isMobile,
    chatId,
    messages.length,
    isUserTemporary,
    adjustHeight,
  ]);

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const { url, pathname, contentType } = data;

        return {
          url,
          name: pathname,
          contentType: contentType,
        };
      }
      const { error } = await response.json();
      toast.error(error);
    } catch (error) {
      toast.error('Failed to upload file, please try again!');
    }
  };

  const handleFileChange = useCallback(
    async (event: ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);

      setUploadQueue(files.map((file) => file.name));

      try {
        const uploadPromises = files.map((file) => uploadFile(file));
        const uploadedAttachments = await Promise.all(uploadPromises);
        const successfullyUploadedAttachments = uploadedAttachments.filter(
          (attachment) => attachment !== undefined,
        );

        setAttachments((currentAttachments) => [
          ...currentAttachments,
          ...successfullyUploadedAttachments,
        ]);
      } catch (error) {
        console.error('Error uploading files!', error);
      } finally {
        setUploadQueue([]);
      }
    },
    [setAttachments],
  );

  // Remove the useEffect that was causing extra height adjustments
  // Height is already adjusted in handleInput

  return (
    <div className="relative w-full flex flex-col gap-4">
      {messages.length === 0 &&
        attachments.length === 0 &&
        uploadQueue.length === 0 && (
          <SuggestedActions append={append} chatId={chatId} />
        )}

      <input
        type="file"
        className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
        ref={fileInputRef}
        multiple
        onChange={handleFileChange}
        tabIndex={-1}
      />

      {(attachments.length > 0 || uploadQueue.length > 0) && (
        <div className="flex flex-row gap-2 overflow-x-scroll items-end">
          {attachments.map((attachment) => (
            <PreviewAttachment key={attachment.url} attachment={attachment} />
          ))}

          {uploadQueue.map((filename) => (
            <PreviewAttachment
              key={filename}
              attachment={{
                url: '',
                name: filename,
                contentType: '',
              }}
              isUploading={true}
            />
          ))}
        </div>
      )}

      <Textarea
        ref={textareaRef}
        placeholder="Share your thoughts..."
        value={input}
        onChange={handleInput}
        className={cx(
          'min-h-fit max-h-[calc(75dvh)] overflow-y-auto resize-none rounded-xl !text-base bg-white border-zinc-400 shadow placeholder:text-zinc-500 pr-10',
          className,
        )}
        rows={3}
        autoFocus
        onKeyDown={(event) => {
          if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();

            if (isLoading) {
              toast.error('Please wait for the model to finish its response!');
            } else {
              submitForm();
            }
          }
        }}
      />

      {isLoading ? (
        <Button
          className="rounded-full p-1.5 h-fit absolute bottom-2 right-2 m-0.5 border dark:border-zinc-600"
          onClick={(event) => {
            event.preventDefault();
            stop();
            setMessages((messages) => sanitizeUIMessages(messages));
          }}
        >
          <StopIcon size={14} />
        </Button>
      ) : (
        <Button
          className="rounded-full p-1.5 h-fit absolute bottom-2 right-2 m-0.5 border dark:border-zinc-600"
          onClick={(event) => {
            event.preventDefault();
            submitForm();
          }}
          disabled={input.length === 0 || uploadQueue.length > 0}
        >
          <ArrowUpIcon size={14} />
        </Button>
      )}

      <Button
        className="rounded-full p-1.5 h-fit absolute bottom-12 right-2 m-0.5 dark:border-zinc-700"
        onClick={(event) => {
          event.preventDefault();
          fileInputRef.current?.click();
        }}
        variant="outline"
        disabled={isLoading}
      >
        <PaperclipIcon size={14} />
      </Button>
    </div>
  );
}

export const MultimodalInput = memo(
  PureMultimodalInput,
  (prevProps, nextProps) => {
    // Fast path: check most likely to change props first
    if (prevProps.input !== nextProps.input) return false;
    if (prevProps.isLoading !== nextProps.isLoading) return false;

    // Check attachments length first (faster than deep comparison)
    if (prevProps.attachments.length !== nextProps.attachments.length) return false;
    if (prevProps.attachments.length > 0 && !equal(prevProps.attachments, nextProps.attachments)) return false;

    // Only check messages length since that's all we use (for SuggestedActions visibility)
    if (prevProps.messages.length !== nextProps.messages.length) return false;

    // Only check other props if the above haven't changed
    if (prevProps.chatId !== nextProps.chatId) return false;

    // Skip function reference checks as they're stable in most cases
    // and checking them can be expensive

    return true;
  },
);
