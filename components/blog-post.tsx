'use client';

import { format } from 'date-fns';
import { motion } from 'framer-motion';
import type { BlogPost as BlogPostType } from '@/lib/blog';

export function BlogPost({ post }: { post: BlogPostType }) {
  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-2xl mx-auto px-4 py-8"
    >
      <header className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{post.title}</h1>
        <time className="text-gray-500 text-sm">
          {format(new Date(post.date), 'MMMM d, yyyy')}
        </time>
        {post.excerpt && (
          <p className="text-lg text-gray-600 dark:text-gray-300 mt-4">
            {post.excerpt}
          </p>
        )}
      </header>
      <div className="prose dark:prose-invert max-w-none">
        <div dangerouslySetInnerHTML={{ __html: post.content }} />
      </div>
    </motion.article>
  );
}
