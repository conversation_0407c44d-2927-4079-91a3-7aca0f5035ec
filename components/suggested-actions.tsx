'use client';

import { motion } from 'framer-motion';
import { Button } from './ui/button';
import type { ChatRequestOptions, CreateMessage, Message } from 'ai';
import { memo } from 'react';

interface SuggestedActionsProps {
  chatId: string;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
}

function PureSuggestedActions({ chatId, append }: SuggestedActionsProps) {
  const suggestedActions = [
    {
      title: 'Stressed',
      label: 'feeling tense and pressured',
      action:
        "Lately I've been feeling really stressed and overwhelmed by everything in my life. I could use some guidance on managing this pressure.",
    },
    {
      title: 'Overwhelmed',
      label: 'struggling to cope with daily tasks',
      action:
        "I've been feeling overwhelmed by all the responsibilities in my life. I don't know how to manage everything.",
    },
    {
      title: 'Depressed',
      label: 'feeling sad and hopeless',
      action:
        "I've been feeling sad and hopeless for a while now. I don't know how to feel better.",
    },
    {
      title: 'Anxious',
      label: 'feeling anxious and worried',
      action:
        "I've been feeling anxious and worried about everything. I'm not sure what to do.",
    },
  ];

  return (
    <div className="grid sm:grid-cols-2 gap-2 w-full">
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className={index > 3 ? 'hidden sm:block' : 'block'}
        >
          <Button
            variant="ghost"
            onClick={async () => {
              window.history.replaceState({}, '', `/chat/${chatId}`);

              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="gradient-primary text-white text-left border shadow-md rounded-xl px-4 py-3.5 flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start"
          >
            <span className="font-medium text-md">{suggestedAction.title}</span>
            <span className="text-muted-foreground text-sm">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
