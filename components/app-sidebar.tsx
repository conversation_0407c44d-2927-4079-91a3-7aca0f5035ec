'use client';

import type { User } from 'next-auth';
import { useRouter, usePathname } from 'next/navigation';

import { PlusIcon } from '@/components/icons';
import { SidebarUserNav } from '@/components/sidebar-user-nav';
import { SidebarJournal } from '@/components/sidebar-journal';
import { SidebarRecollections } from '@/components/sidebar-recollections';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';

export function AppSidebar({ user }: { user: User | undefined }) {
  const router = useRouter();
  const { setOpenMobile } = useSidebar();
  const pathname = usePathname();
  const isJournalPage = pathname?.startsWith('/journal');
  const href = isJournalPage ? '/journal-new' : '/';

  return (
    <Sidebar className="group-data-[side=left]:border-r-0 bg-sidebar">
      <SidebarHeader>
        {isJournalPage ? (
          <Link
            href={href}
            onClick={() => {
              setOpenMobile(false);
            }}
            className="flex flex-row items-center"
          >
            <SidebarMenu className="gradient-primary text-white rounded shadow-md p-2">
              <div className="flex flex-row justify-between items-center">
                <PlusIcon />
                <span className="text-sm font-semibold px-2 hover:bg-muted rounded-md cursor-pointer">
                  New Journal
                </span>
              </div>
            </SidebarMenu>
          </Link>
        ) : (
          <div className="p-2">
            <h2 className="text-lg font-semibold text-sidebar-foreground">Recollections</h2>
          </div>
        )}
      </SidebarHeader>
      <SidebarContent>
        {isJournalPage ? (
          <SidebarJournal user={user} />
        ) : (
          <SidebarRecollections user={user} />
        )}
      </SidebarContent>
      <SidebarFooter isLoggedIn={!!user}>
        {user && <SidebarUserNav user={user} />}
      </SidebarFooter>
    </Sidebar>
  );
}
