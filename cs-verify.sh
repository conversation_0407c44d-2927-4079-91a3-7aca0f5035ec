#!/bin/bash
# <PERSON> Squad Diagnostic Script
# This script helps diagnose common Claude Squad startup issues

echo "🔍 CLAUDE SQUAD DIAGNOSTIC SCRIPT"
echo "=================================="
echo

# Function to check command availability
check_command() {
    if command -v "$1" >/dev/null 2>&1; then
        echo "✅ $1 is installed"
    else
        echo "❌ $1 is NOT installed"
        return 1
    fi
}

# Function to check Git repository status
check_git_repo() {
    echo "📁 CHECKING GIT REPOSITORY"
    echo "------------------------"

    if [ -d ".git" ]; then
        echo "✅ .git directory found"

        if git status >/dev/null 2>&1; then
            echo "✅ Valid Git repository"

            if git remote -v | grep -q origin; then
                echo "✅ Remote origin configured"
                git remote -v
            else
                echo "❌ No remote origin found"
                echo "   FIX: git remote add origin <your-repo-url>"
            fi
        else
            echo "❌ Git repository is corrupted"
        fi
    else
        echo "❌ Not in a Git repository"
        echo "   FIX: cd to your project directory or run 'git init'"
    fi
    echo
}

# Function to check tmux status
check_tmux() {
    echo "🖥️  CHECKING TMUX STATUS"
    echo "----------------------"

    if command -v tmux >/dev/null 2>&1; then
        echo "✅ tmux is installed"

        # Check existing sessions
        if tmux ls 2>/dev/null; then
            echo "⚠️  Existing tmux sessions found (may cause conflicts)"
            echo "   FIX: tmux kill-server"
        else
            echo "✅ No conflicting tmux sessions"
        fi

        # Check for zombie tmux processes
        tmux_procs=$(ps aux | grep tmux | grep -v grep | wc -l)
        if [ "$tmux_procs" -gt 0 ]; then
            echo "⚠️  $tmux_procs tmux processes running"
            ps aux | grep tmux | grep -v grep
        else
            echo "✅ No tmux processes running"
        fi
    else
        echo "❌ tmux is NOT installed"
        echo "   FIX: Install tmux (sudo apt install tmux or brew install tmux)"
    fi
    echo
}

# Function to check Claude Squad installation
check_claude_squad() {
    echo "🤖 CHECKING CLAUDE SQUAD"
    echo "------------------------"

    if command -v cs >/dev/null 2>&1; then
        echo "✅ Claude Squad (cs) command found"
    else
        echo "❌ Claude Squad (cs) command not found"
        echo "   FIX: Install Claude Squad or check PATH"
    fi

    if command -v go >/dev/null 2>&1; then
        echo "✅ Go is installed: $(go version)"
    else
        echo "❌ Go is NOT installed"
        echo "   FIX: Install Go programming language"
    fi
    echo
}

# Function to check system resources
check_resources() {
    echo "💾 CHECKING SYSTEM RESOURCES"
    echo "----------------------------"

    # Check disk space
    df_output=$(df -h . | tail -1)
    echo "💿 Disk space: $df_output"

    # Check memory
    if command -v free >/dev/null 2>&1; then
        echo "🧠 Memory usage:"
        free -h
    fi

    # Check temp directory permissions
    if [ -w "/tmp" ]; then
        echo "✅ /tmp directory is writable"
    else
        echo "❌ /tmp directory is not writable"
        echo "   FIX: Check permissions on /tmp"
    fi
    echo
}

# Function to suggest fixes
suggest_fixes() {
    echo "🔧 SUGGESTED FIXES"
    echo "=================="
    echo
    echo "1. Clean tmux sessions:"
    echo "   tmux kill-server"
    echo "   sudo rm -rf /tmp/tmux-*"
    echo
    echo "2. Ensure you're in a Git repository:"
    echo "   cd /path/to/your/project"
    echo "   git status"
    echo
    echo "3. Add Git remote if missing:"
    echo "   git remote add origin <your-repo-url>"
    echo
    echo "4. Restart Claude Squad:"
    echo "   cs"
    echo
    echo "5. If problems persist:"
    echo "   - Check Claude Squad logs"
    echo "   - Restart your terminal"
    echo "   - Reboot your system"
    echo
}

# Main execution
main() {
    echo "Starting diagnostic..."
    echo "Current directory: $(pwd)"
    echo "Current user: $(whoami)"
    echo "Date: $(date)"
    echo

    check_git_repo
    check_tmux
    check_claude_squad
    check_resources
    suggest_fixes

    echo "🎯 DIAGNOSTIC COMPLETE"
    echo "If issues persist, provide this output when seeking help."
}

# Run main function
main
