CREATE TABLE IF NOT EXISTS "DebugChat" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "createdAt" timestamp NOT NULL,
  "title" text NOT NULL,
  "userId" uuid NOT NULL,
  "visibility" varchar DEFAULT 'private' NOT NULL,
  "deleted" boolean DEFAULT false NOT NULL,
  "isVoiceChat" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DebugChat" ADD CONSTRAINT "DebugChat_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
-- Update the DebugMessage table to reference DebugChat instead of Chat
ALTER TABLE "DebugMessage" DROP CONSTRAINT IF EXISTS "DebugMessage_chatId_Chat_id_fk";
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DebugMessage" ADD CONSTRAINT "DebugMessage_chatId_DebugChat_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."DebugChat"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$; 