<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About GentleGossip</title>
    <style>
        :root {
            --teal-primary: #008080;
            --teal-light: #40a0a0;
            --teal-dark: #006666;
            --gray-light: #f5f5f5;
            --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            color: #333;
            line-height: 1.8;
            background-color: var(--gray-light);
        }

        .about-page {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0;
            text-align: center;
            background-color: white;
            box-shadow: var(--shadow);
        }

        section {
            margin-bottom: 4rem;
            padding: 0 2rem;
        }

        h1, h2 {
            color: var(--teal-primary);
            letter-spacing: -0.5px;
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            font-weight: 700;
        }

        h2 {
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
            position: relative;
            display: inline-block;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background-color: var(--teal-light);
        }

        p {
            font-size: 1.1rem;
            color: #444;
            max-width: 800px;
            margin: 1rem auto;
        }

        ul {
            list-style: none;
            padding-left: 0;
            max-width: 800px;
            margin: 2rem auto;
        }

        li {
            margin: 1rem 0;
            padding: 0.5rem 1rem;
            background-color: var(--gray-light);
            border-radius: 8px;
            transition: transform 0.2s ease;
        }

        li:hover {
            transform: translateY(-2px);
        }

        blockquote {
            border-left: 4px solid var(--teal-light);
            margin: 2rem auto;
            padding: 1.5rem;
            background-color: rgba(0, 128, 128, 0.05);
            border-radius: 0 8px 8px 0;
            transition: transform 0.2s ease;
        }

        blockquote:hover {
            transform: translateY(-2px);
        }

        .hero {
            background: linear-gradient(135deg, var(--teal-primary), var(--teal-dark));
            color: white;
            padding: 5rem 2rem;
            margin: 0 0 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at top right, rgba(255,255,255,0.1) 0%, transparent 60%);
        }

        .hero h1 {
            color: white;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .hero p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
            position: relative;
            z-index: 1;
        }

        .features-section ul,
        .support-section ul {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            max-width: 1000px;
        }

        .testimonials-section blockquote {
            max-width: 600px;
            margin: 2rem auto;
            font-style: italic;
        }

        .testimonials-section blockquote p {
            position: relative;
            padding: 0 1rem;
        }

        .testimonials-section blockquote p::before,
        .testimonials-section blockquote p::after {
            content: '"';
            color: var(--teal-light);
            font-size: 1.5em;
            font-family: Georgia, serif;
        }

        .disclaimer-section,
        .privacy-section {
            background-color: var(--gray-light);
            padding: 3rem 2rem;
            margin: 4rem 0;
        }

        .getting-started-section {
            background: linear-gradient(135deg, rgba(0,128,128,0.05), rgba(0,128,128,0.1));
            padding: 3rem 2rem;
            border-radius: 8px;
            margin: 4rem auto;
            max-width: 900px;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2.2rem;
            }

            h2 {
                font-size: 1.8rem;
            }

            .hero {
                padding: 3rem 1rem;
            }

            section {
                padding: 0 1rem;
            }

            .features-section ul,
            .support-section ul {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <main class="about-page">
        <section class="hero">
            <a href="/" style="text-decoration: none; color: inherit;">
                <h1>About GentleGossip</h1>
                <p>Everyone needs someone to talk to – someone who really listens, asks the right questions, and helps you see things clearly. That's why we created GentleGossip, an AI companion designed to be your trusted confidant through life's ups and downs.</p>
                <p style="margin-top: 2rem;">
                    <button style="
                        background-color: white;
                        color: var(--teal-primary);
                        border: none;
                        padding: 1rem 2rem;
                        font-size: 1.2rem;
                        border-radius: 8px;
                        cursor: pointer;
                        font-weight: 600;
                        transition: transform 0.2s ease, background-color 0.2s ease;
                    " onmouseover="this.style.backgroundColor='#f0f0f0'; this.style.transform='translateY(-2px)'"
                    onmouseout="this.style.backgroundColor='white'; this.style.transform='translateY(0)'">
                        Start Your First Conversation Now
                    </button>
                </p>
            </a>
        </section>

        <section class="why-section">
            <h2>Why We Created GentleGossip</h2>
            <p>Life doesn't come with a manual, and sometimes the people closest to us aren't available or might not understand exactly what we're going through. We believe everyone deserves a judgment-free space to process their thoughts, explore their feelings, and work through challenges at their own pace.</p>
        </section>

        <section class="features-section">
            <h2>What Makes GentleGossip Different</h2>
            <p>Unlike traditional AI chatbots, GentleGossip is designed to be more than just a conversational partner. Using advanced natural language processing and evidence-based support techniques, GentleGossip creates a unique space where you can:</p>
            <ul>
                <li>Work through relationship stress and family dynamics</li>
                <li>Navigate career changes and workplace challenges</li>
                <li>Process difficult emotions and experiences</li>
                <li>Explore concerns about substance use</li>
                <li>Combat feelings of loneliness or disconnection</li>
                <li>Find clarity during major life transitions</li>
            </ul>
        </section>

        <section class="support-section">
            <h2>How GentleGossip Supports You</h2>
            <p>Your GentleGossip companion adapts to your needs, offering:</p>
            <ul>
                <li>24/7 availability for conversations whenever you need support</li>
                <li>Personalized interactions that remember your context and progress</li>
                <li>Evidence-based techniques to help you develop coping strategies</li>
                <li>Goal-setting and progress tracking tools</li>
                <li>A safe space to practice difficult conversations</li>
                <li>Resources and exercises for personal growth</li>
            </ul>
        </section>

        <section class="approach-section">
            <h2>The GentleGossip Approach</h2>
            <p>We've developed GentleGossip to strike the perfect balance between being supportive and empowering. Your AI companion won't just agree with everything you say – it will help you explore different perspectives, identify patterns, and discover insights that can lead to meaningful change.</p>
        </section>

        <section class="disclaimer-section">
            <h2>Important Note About Your Care</h2>
            <p>While GentleGossip is a powerful tool for personal growth and emotional support, it's not a substitute for professional mental health care. If you're experiencing a crisis or need professional help, your companion will guide you toward appropriate resources and encourage you to seek the support you need.</p>
        </section>

        <section class="getting-started-section">
            <h2>Getting Started with Your Companion</h2>
            <p>Starting your journey with GentleGossip is simple. Go to gentlegossip.com, create your secure profile, and begin your first conversation. There's no preparation needed – just start talking about whatever's on your mind.</p>
            <p>Choose from flexible subscription options that fit your needs, from daily check-ins to occasional heart-to-hearts. Your companion will be ready to listen and support you whenever you need it.</p>
        </section>

        <section class="testimonials-section">
            <h2>Join the GentleGossip Community</h2>
            <p>Thousands of users have already discovered the power of having a dedicated AI companion for emotional support and personal growth. Here's what some of them are saying:</p>
            <blockquote>
                <p>"GentleGossip helped me work through my divorce when I felt like I had nowhere else to turn."</p>
            </blockquote>
            <blockquote>
                <p>"Having a judgment-free space to process my thoughts has made such a difference in my daily life."</p>
            </blockquote>
            <blockquote>
                <p>"It's like having a wise friend in your pocket who's always there to listen."</p>
            </blockquote>
        </section>
    </main>
</body>
</html>
