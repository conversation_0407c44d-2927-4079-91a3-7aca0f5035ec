'use client';

import { useState } from 'react';
import { LoaderIcon } from '@/components/icons';
import { Button } from './ui/button';

export function SubmitButton({
  children,
  isSuccessful,
  onClick,
}: {
  children: React.ReactNode;
  isSuccessful: boolean;
  onClick?: () => void;
}) {
  const [isPending, setIsPending] = useState(false);

  const handleClick = async () => {
    if (onClick) {
      setIsPending(true);
      try {
        await onClick();
      } finally {
        setIsPending(false);
      }
    }
  };

  return (
    <Button
      type="submit"
      aria-disabled={isPending || isSuccessful}
      disabled={isPending || isSuccessful}
      onClick={handleClick}
      className="auth-btn"
    >
      {children}
      {(isPending || isSuccessful) && (
        <span className="animate-spin absolute right-4">
          <LoaderIcon />
        </span>
      )}
    </Button>
  );
}
