<a href="https://gentlegossip.com">
  <h1 align="center">GentleGossip AI Chatbot</h1>
</a>

<p align="center">
  <a href="#features"><strong>Features</strong></a> ·
  <a href="#model-providers"><strong>Model Providers</strong></a> ·
  <a href="#deploy-your-own"><strong>Deploy Your Own</strong></a> ·
  <a href="#running-locally"><strong>Running locally</strong></a>
</p>
<br/>

## Architecture

```
                         +---------------------------+
 (HTTP/SSL)              |      NEXT.JS APP         |
+------------+           | (App Router, RSC, SSR)   |
|  BROWSER   |---------->| - NextAuth (Auth)        |
|(Front-End) |           | - Drizzle ORM            |
+------------+           | - AI Integration         |
        ^                |   (via AI SDK)           |
        |                +-----------+--------------+
        |                            |
        | (Server-Side)              v
        |                   +----------------------+
        |                   |   DATABASE (Pg)      |
        |                   | (Vercel Postgres)    |
        |                   +----------------------+
        |
        |       (HTTPS to External Providers)
        v
+----------------+         +---------------------------------------+
|     AI SDK     | <-----> | External AI Providers (OpenAI, etc.)  |
| (Library Code) |         |  (Anthropic, Cohere, etc.)            |
+----------------+         +---------------------------------------+
```

### Architecture Components

1. **Browser (Front-End)**
   - User interface accessed via web browsers
   - Communicates with Next.js backend via HTTP/SSL

2. **Next.js App**
   - Built with App Router, React Server Components (RSCs) and Server-Side Rendering (SSR)
   - Integrates NextAuth for authentication
   - Uses Drizzle ORM for database operations
   - AI integration via AI SDK

3. **Database**
   - Vercel Postgres for data persistence
   - Accessed via Drizzle ORM

4. **AI Integration**
   - AI SDK for standardized LLM interactions
   - Connects to external providers via HTTPS
   - Supports OpenAI, Anthropic, Cohere, and others


## Data Flow Diagram

```
+---------------+     1. User Message     +-------------+
|               |-------------------->    |             |
|    Browser    |                         |  Next.js    |
|    Client     |     5. Stream UI        |    App      |
|               |<--------------------    |             |
+---------------+                         +-------------+
                                              |
                                              | 2. Server Action
                                              |
                                              v
                                        +-------------+
                                        |             |
                                        | Vercel AI   |
                                        |    SDK      |<---+
                                        |             |    |
                                        +-------------+    |
                                              |            |
                                     3. API   |            | 4. Stream
                                    Request   |            | Response
                                              v            |
+---------------+                    +-------------+       |
|   Vercel      |    6. Save         |             |       |
|   Postgres    |<---------------    | LLM API     |-------+
|   Database    |                    |             |
+---------------+                    +-------------+

Data Flow Steps:
1. User sends a message through the browser interface
2. Next.js processes the request via Server Action
3. Vercel AI SDK formats and sends request to LLM api
4. LLM streams response back to Vercel AI SDK
5. Browser receives streamed response and updates chat interface
6. Chat history is saved to Vercel Postgres database

## Features

- [Next.js](https://nextjs.org) App Router
  - Advanced routing for seamless navigation and performance
  - React Server Components (RSCs) and Server Actions for server-side rendering and increased performance
- [AI SDK](https://sdk.vercel.ai/docs)
  - Unified API for generating text, structured objects, and tool calls with LLMs
  - Hooks for building dynamic chat and generative user interfaces
  - Supports OpenAI (default), Anthropic, Cohere, and other model providers
- [shadcn/ui](https://ui.shadcn.com)
  - Styling with [Tailwind CSS](https://tailwindcss.com)
  - Component primitives from [Radix UI](https://radix-ui.com) for accessibility and flexibility
- Data Persistence
  - [Vercel Postgres powered by Neon](https://vercel.com/storage/postgres) for saving chat history and user data
  - [Vercel Blob](https://vercel.com/storage/blob) for efficient file storage
- [NextAuth.js](https://github.com/nextauthjs/next-auth)
  - Simple and secure authentication
- Zero-Infrastructure Prompt Caching System
  - Uses Anthropic's native `cache_control` feature for all models
  - Reduces API costs and improves response times

## Model Providers

This template ships with OpenAI `gpt-4o` as the default. However, with the [AI SDK](https://sdk.vercel.ai/docs), you can switch LLM providers to [OpenAI](https://openai.com), [Anthropic](https://anthropic.com), [Cohere](https://cohere.com/), and [many more](https://sdk.vercel.ai/providers/ai-sdk-providers) with just a few lines of code.

## Running locally

You will need to use the environment variables [defined in `.env.example`](.env.example) to run Next.js AI Chatbot. It's recommended you use [Vercel Environment Variables](https://vercel.com/docs/projects/environment-variables) for this, but a `.env` file is all that is necessary.

> Note: You should not commit your `.env` file or it will expose secrets that will allow others to control access to your various OpenAI and authentication provider accounts.

1. Install Vercel CLI: `npm i -g vercel`
2. Link local instance with Vercel and GitHub accounts (creates `.vercel` directory): `vercel link`
3. Download your environment variables: `vercel env pull`

```bash
pnpm install
pnpm dev
```

Your app template should now be running on [localhost:3000](http://localhost:3000/).
