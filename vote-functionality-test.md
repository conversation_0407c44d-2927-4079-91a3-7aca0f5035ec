# Vote Functionality Test Plan

## Overview
This document outlines the test plan for verifying the voting functionality in the GentleGossip application.

## Components to Test

### 1. Database Schema
- [x] Vote table exists with correct structure
- [x] Primary key is composite (chatId, messageId)
- [x] Foreign key constraints to Chat and Message tables
- [x] isUpvoted boolean field

### 2. API Endpoints

#### GET /api/vote
- [ ] Returns votes for a valid chatId
- [ ] Returns 400 for missing chatId
- [ ] Returns 400 for invalid UUID format
- [ ] Returns 401 for unauthorized users
- [ ] Returns 500 for database errors

#### PATCH /api/vote
- [ ] Creates new vote successfully
- [ ] Updates existing vote successfully
- [ ] Returns 400 for missing required fields
- [ ] Returns 400 for invalid UUID formats
- [ ] Returns 401 for unauthorized users
- [ ] Returns 500 for database errors

### 3. Database Functions

#### voteMessage()
- [ ] Creates new vote when none exists
- [ ] Updates existing vote when one exists
- [ ] Handles upvote (type: 'up') correctly
- [ ] Handles downvote (type: 'down') correctly
- [ ] Validates chatId and messageId match

#### getVotesByChatId()
- [ ] Returns all votes for a chat
- [ ] Returns empty array for chat with no votes
- [ ] Handles invalid chatId gracefully

### 4. Frontend Components

#### MessageActions Component
- [ ] Shows upvote and downvote buttons for assistant messages
- [ ] Hides voting buttons for user messages
- [ ] Hides voting buttons for messages with tool invocations
- [ ] Disables upvote button when message is already upvoted
- [ ] Disables downvote button when message is already downvoted
- [ ] Shows loading state during vote submission
- [ ] Shows success/error toasts appropriately
- [ ] Updates local state optimistically

#### Vote State Management
- [ ] Fetches votes on chat load
- [ ] Updates vote state after successful vote
- [ ] Handles vote state correctly in SWR cache
- [ ] Revalidates votes appropriately

### 5. Message ID Handling
- [ ] getMessageIdFromAnnotations() extracts correct ID
- [ ] Falls back to message.id when no annotation
- [ ] Validates UUID format before using server ID
- [ ] Handles missing annotations gracefully

### 6. User Authentication
- [ ] Works with authenticated users
- [ ] Works with temporary users
- [ ] Blocks unauthorized access

## Test Scenarios

### Scenario 1: First Vote on Message
1. Navigate to a chat with assistant messages
2. Click upvote on an unvoted message
3. Verify button becomes disabled
4. Verify vote is saved to database
5. Verify UI reflects the vote state

### Scenario 2: Changing Vote
1. Start with an upvoted message
2. Click downvote button
3. Verify vote changes from up to down
4. Verify database is updated
5. Verify UI reflects new state

### Scenario 3: Multiple Messages
1. Vote on multiple messages in same chat
2. Verify each vote is independent
3. Verify votes persist across page refreshes
4. Verify vote counts are accurate

### Scenario 4: Error Handling
1. Test with invalid message IDs
2. Test with network failures
3. Test with database errors
4. Verify appropriate error messages

## Manual Testing Checklist

### Prerequisites
- [ ] Application running on localhost:3001
- [ ] Database accessible
- [ ] User authenticated or temp user created

### Test Steps
1. [ ] Create a new chat
2. [ ] Send a message to get assistant response
3. [ ] Verify vote buttons appear on assistant message
4. [ ] Test upvote functionality
5. [ ] Test downvote functionality
6. [ ] Test vote persistence across refresh
7. [ ] Test multiple votes in same chat
8. [ ] Test error scenarios

## Expected Results

### Database
- Vote records created/updated correctly
- Composite primary key prevents duplicates
- Foreign key constraints maintained

### API
- Proper HTTP status codes returned
- Correct JSON responses
- Appropriate error handling

### Frontend
- Buttons disabled/enabled correctly
- Loading states shown
- Toast notifications displayed
- Optimistic updates work

## Issues Found

### 1. Database Query Issue (FIXED)
- **Problem**: `voteMessage` function was only checking `messageId` in WHERE clause, not the composite key `(chatId, messageId)`
- **Impact**: Could lead to incorrect vote updates if same messageId exists in different chats
- **Fix**: Updated query to use both `chatId` and `messageId` in WHERE clause

### 2. Optimistic Update Mismatch (FIXED)
- **Problem**: Frontend was using `message.id` in optimistic updates but `getMessageIdFromAnnotations(message)` in API calls
- **Impact**: Optimistic updates wouldn't match actual API responses, causing UI inconsistencies
- **Fix**: Updated optimistic updates to use `getMessageIdFromAnnotations(message)` consistently

### 3. Foreign Key Constraint Violations
- **Problem**: Voting on messages before chat is properly saved to database
- **Impact**: PostgreSQL foreign key constraint violations causing 500 errors
- **Fix**: Added validation to check chat and message existence before voting

### 4. Poor Error Handling (FIXED)
- **Problem**: Generic error messages for foreign key violations
- **Impact**: Users get unhelpful "Failed to vote message" errors
- **Fix**: Added specific error handling for chat/message not found scenarios

## Test Results

### API Validation Tests ✅
- Missing chatId returns 400 ✅
- Invalid UUID format returns 400 ✅
- Missing required fields returns 400 ✅
- Authentication required (401 for unauthenticated) ✅

### Database Fixes ✅
- Composite key query fixed ✅
- Validation for chat/message existence added ✅
- Better error messages implemented ✅

### Frontend Fixes ✅
- Optimistic update consistency fixed ✅
- Message ID extraction unified ✅

## Recommendations

### 1. Add Integration Tests
- Create proper test suite with authentication
- Test vote creation, updates, and persistence
- Test error scenarios with real database

### 2. Add Loading States
- Show loading indicators during vote operations
- Disable buttons during API calls to prevent double-voting

### 3. Add Vote Counts Display
- Consider showing vote counts/statistics
- Add analytics for vote patterns

### 4. Improve Error Recovery
- Add retry mechanisms for failed votes
- Better user feedback for network errors

### 5. Performance Optimizations
- Consider caching vote data
- Batch vote operations if needed
- Add database indexes for vote queries
